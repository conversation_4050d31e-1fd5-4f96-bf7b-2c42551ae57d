# Apple Push Notifications

Production certification keys for push notifications.
Folder `./keys` is ignored from git.


## XCode Simulator

```shell
SERVICE_ID="demo_svc_01jrwnknw6179cm1t6mcpb59rx"
DATE=$(date "+%Y-%m-%d %H:%M:%S")
RANDOM_ID=$((RANDOM % 90000 + 10000))  # generates 10000–99999
printf '%0.s-' {1..200}
echo "\nDATE: $DATE"
echo "Notification ID: ntf_000000000000000000000$RANDOM_ID"
echo '{
  "aps": {
    "alert": {
      "title": "Title at '"$DATE"'",
      "subtitle": "Notification title",
      "body": "This is an inline push notification test."
    },
    "mutable-content": 1,
    "thread-id": "'"$SERVICE_ID"'",
    "category": "APPIO_DEFAULT",
    "sound": "default"
  },
  "data": {
    "notification_id": "ntf_000000000000000000000'"$RANDOM_ID"'",
    "service_id": "'"$SERVICE_ID"'",
    "logo": "https://cdn.appio.so/app/demo.appio.so/logo.png",
    "image_url": "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
    "link": "https://appio.so"    
  }
}' | xcrun simctl push booted so.appio.app -
```


## Generation

Generated at: https://developer.apple.com/account/resources/authkeys/add
Name: Appio APNs Key
Description: Apple Push Notifications service
Enable: Apple Push Notifications service (APN)
  Topic: so.appio.app
  Environment: Sandbox


## Sending

`./send-notification-sandbox.sh`


### Docs

Request docs:  https://developer.apple.com/documentation/usernotifications/sending-notification-requests-to-apns
Payload docs:  https://developer.apple.com/documentation/usernotifications/generating-a-remote-notification
Response docs: https://developer.apple.com/documentation/usernotifications/handling-notification-responses-from-apns
Live Activity: https://developer.apple.com/documentation/ActivityKit/starting-and-updating-live-activities-with-activitykit-push-notifications


### Headers 

-H "apns-push-type: alert"      https://developer.apple.com/documentation/usernotifications/sending-notification-requests-to-apns#Know-when-to-use-push-types
                                alert = visible notificaitons
                                background = deliver content in bg, no interaction. priority should be 5, never 10. https://developer.apple.com/documentation/usernotifications/pushing-background-updates-to-your-app
                                controls = ios 18 https://developer.apple.com/documentation/WidgetKit/Updating-controls-locally-and-remotely
                                complication = watchOS, apns-topic header must use BundleID with .complication appended to the end. https://developer.apple.com/documentation/ClockKit/keeping-your-complications-up-to-date
                                liveactivity = . https://developer.apple.com/documentation/ActivityKit/starting-and-updating-live-activities-with-activitykit-push-notifications
-H "apns-priority: 10"                                  10 = immediate
                                                        5 = based on device power consideration
                                                        1 = prioritise device power, prevent awaking device
-H "apns-expiration: $(($(date +%s) + 3600))"           retry delivery limit: 0 = immediate discard. Apple sets this. max is 1 year
-H "apns-id: f13408ab-15a1-45eb-9fa2-71f50963f839"      UUID, if omitted apple generates one


### Payload

launch-image    doesn't work if i am using custom launch storyboard



## Server Golang

https://github.com/sideshow/apns2
fork: https://github.com/caiyunapp/apns2


