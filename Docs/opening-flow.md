# Opening flow

## Starting parameters
- service id = s_id
- customer user id = cu_id
- device id = d_id

## Available functions
- register device
  input: s_id, cu_id, notificaions_enabled=PushNotificationManager.isEnabled()
  output: d_id
- match fingerprint
  input: device info (DeviceInfoProvider.getDeviceInfo())
  output: s_id, cu_id
- link device with service
  input: d_id, s_id, cu_id
  output: bool (success)
- fetch service
  input: s_id
  output: service (json: title, description, logoUrl, bannerUrl)
- refresh services
  input: d_id
  output: array of services (service json: title, description, logoUrl, bannerUrl)

## Screens
- loading: default starting screen
- intro: screen with option to open browser of QR code
- service: screen with service details (title, description, logoUrl, bannerUrl)
- all_service: screen showing list of all registered services


## Flow
Actions on the same level are executed in parallel.
These actions should be triggered for both cold and warm open.

--- blank cold open. f.e. downloading and opening app directly from app store.
param values: d_id=nil, s_id=nil, cu_id=nil
param values: d_id=nil, s_id=VAL, cu_id=nil (invalid state. s_id is ignored)
param values: d_id=nil, s_id=nil, cu_id=VAL (invalid state. cu_id is ignored)
action: match fingerprint:
  -> if not: screen=intro
  -> if ok: s_id=VAL, cu_id=VAL
    action: register device:
      -> if not: ERROR
      -> if ok: d_id=VAL, update storage
    action: fetch service:
      -> if not: ERROR
      -> if ok: update storage, screen=service

--- reopening app with registered device and service. 
param values: d_id=VAL, s_id=nil, cu_id=nil
param values: d_id=VAL, s_id=nil, cu_id=VAL (invalid state. cu_id is ignored)
action: refresh services
  -> if not: ERROR
  -> if ok: update storage, screen=all_service

--- reopening app with registered device and service. open specified service
param values: d_id=VAL, s_id=VAL, cu_id=nil
action: refresh services
  -> if not: ERROR
  -> if ok: s_id=VAL, update storage, screen=service

--- registering new service
param values: d_id=VAL, s_id=VAL, cu_id=VAL
action: link device with service
  -> if not: ERROR
  -> if ok: screen=service
action: refresh services
  -> if not: ERROR
  -> if ok: update storage

--- registering first service
param values: d_id=nil, s_id=VAL, cu_id=VAL
action: register device:
  -> if not: ERROR
  -> if ok: d_id=VAL, update storage
action: fetch service:
  -> if not: ERROR
  -> if ok: update storage, screen=service












