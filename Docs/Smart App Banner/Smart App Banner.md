# Smart App Banner

## Smart banner
`app-argument` only works if app is installed
documentation: https://developer.apple.com/documentation/webkit/promoting_apps_with_smart_app_banners

Install triggered automatically, no redirection to app store
Example: https://www.komoot.com

### Clicks flow 
1. “Mobile App” on Service page to install app
    1.1. scan QR code to launch Appio Service page and continue with mobile flow
2. “GET” on Smart banner on Appio Service page
3. “OPEN” on Smart banner on Appio Service page

### Visibility
Based on routes defined in: https://app.appio.so/.well-known/apple-app-site-association
Cached in: https://app-site-association.cdn-apple.com/a/v1/app.appio.so
Developer mode: applinks:app.appio.so?mode=developer
!! mode must be removed before submitting to App Store


## Button

Button changed from "View" to "GET"

In 2023 View was still used (this was iOS 16, as 17 was ust being introduced)
https://www.youtube.com/watch?v=Sb0HPomn8sQ&t=700
https://developer.apple.com/videos/play/tech-talks/110358/

As of 2/1/2025 the official Apple docs mentions "View" button instead of "GET"
https://developer.apple.com/documentation/webkit/promoting-apps-with-smart-app-banners

iOS 16 changed install button from "View" to "GET" button within Smart Banner (no App Store redirects)

