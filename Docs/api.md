# API

Auth key is set in /var/www/app.appio.so/configs/config.toml


## Fingerprint: Match

```shell
curl -X POST https://api.appio.so/ios/fingerprints/match \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "Content-Type: application/json" \
-d '{"user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "screen_resolution":"1792x1120", "language":"en-GB", "time_offset":-60}'
```

Response:
```json
{
  "fingerprint_id": "fing_00000000000000000000000000",
  "service_id": "svc_00000000000000000000000000",
  "customer_user_id": "customer_0000000000"
}
```

## Device: Registration

```shell
curl -i -X POST https://api.appio.so/ios/devices \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_00000000000000000000000000" \
-H "Content-Type: application/json" \
-d '{"customer_user_id": "cli-test", "name": "iPhone", "platform": "ios", "os_version": "18.3.1", "model": "iPhone", "notifications_enabled": true, "device_identifier": "iPhone17,5"}'
```

Response:
```json
{
  "id": "dvc_00000000000000000000000000"
}
```
 

## NEW: Device: Update

```shell
curl -i -X PATCH https://api.appio.so/ios/devices/dvc_00000000000000000000000000 \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "Content-Type: application/json" \
-d '{"notifications_enabled":true,"device_token":"123"}'
```

Response:
```json
  "id": "dvc_00000000000000000000000000"
```


## Device: Linking service

```shell
curl -i -X POST https://api.appio.so/ios/devices/dvc_00000000000000000000000000/services \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_11111111111111111111111111" \
-H "Content-Type: application/json" \
-d '{"customer_user_id": "customer_1111111111"}'
```

Response:
```json
{
  "id": "dvc_00000000000000000000000000"
}
```

## Device: Detach from service

```shell
curl -i -X DELETE https://api.appio.so/ios/devices/dvc_00000000000000000000000000 \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_11111111111111111111111111"
```

Response:
```json
{
  "id": "dvc_00000000000000000000000000"
}
```


## Service: Get one

```shell
curl -i -X GET https://api.appio.so/ios/services/svc_00000000000000000000000000 \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_00000000000000000000000000"
```

Response:
```json
{
  "id": "svc_00000000000000000000000000",
  "title": "new title. 24eac451-3e0d-4901-984a-38e380c2bfeb",
  "description": "new description. 2025-02-27T13:54:12.629372Z",
  "logo_url": "https://cdn.appio.so/app/appio.so/logo.png",
  "banner_url": "https://cdn.appio.so/app/appio.so/banner.jpg"
}
```


## Serice: List all by device id

```shell
curl -L -i -X GET https://api.appio.so/ios/services \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Device-Id: dvc_00000000000000000000000000"
```

Response:
```json
[
  {
    "id": "svc_00000000000000000000000000",
    "title": "new title. 24eac451-3e0d-4901-984a-38e380c2bfeb",
    "description": "new description. 2025-02-27T13:54:12.629372Z",
    "logo_url": "https://cdn.appio.so/app/appio.so/logo.png",
    "banner_url": "https://cdn.appio.so/app/appio.so/banner.jpg",
    "widgets": [
      {
        "id": "wgt_00000000000000000000000000",
        "service_id": "svc_00000000000000000000000000",
        "config": "..."
      }
    ]
  },
  {
    "id": "svc_00000000000000001111111111",
    "title": "new title. 24eac451-3e0d-4901-984a-38e380c2bfeb",
    "description": "new description. 2025-02-27T13:54:12.629372Z",
    "logo_url": "https://cdn.appio.so/app/appio.so/logo.png",
    "banner_url": "https://cdn.appio.so/app/appio.so/banner.jpg",
    "widgets": [
      {
        "id": "wgt_00000000000000001111111111",
        "service_id": "svc_00000000000000001111111111",
        "config": "..."
      }
    ]
  }
]
```


## Notification: List all by service id and device id

```shell
curl -i -X GET https://api.appio.so/ios/notifications \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_00000000000000000000000000" \
-H "X-Device-Id: dvc_00000000000000000000000000"
```

Response:
```json
[
  {
    "id": "ntf_00000000000000000000000000",
    "service_id": "svc_00000000000000000000000000",
    "status": "created",
    "payload": {
      "message": "Hello world!"
    },
    "scheduled_at": "2025-03-20T13:47:04.306269Z",
    "sent_at": "2025-03-20T13:47:04.306269Z"
  },
  {
    "id": "ntf_00000000000000001111111111",
    "service_id": "svc_00000000000000000000000000",
    "status": "created",
    "payload": {
      "message": "Hello world!"
    },
    "scheduled_at": "2025-03-20T13:47:04.306269Z",
    "sent_at": "2025-03-20T13:47:04.306269Z"
  },
  {
    "id": "ntf_00000000000000002222222222",
    "service_id": "svc_00000000000000000000000000",
    "status": "completed",
    "payload": {
      "message": "Goodbye planet?"
    },
    "scheduled_at": "2025-03-20T13:47:04.306269Z",
    "sent_at": "2025-03-20T13:47:04.306269Z"
  }
]
```


## Widget: Get one

```shell
curl -i -X GET https://api.appio.so/ios/widgets/wgt_00000000000000000000000000 \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-Service-Id: svc_00000000000000000000000000"
```

Response:
```json
{
  "id": "wgt_00000000000000000000000000",
  "service_id": "svc_00000000000000000000000000",
  "config": "...",
}
```


## Feature Flags: Get config for version

```shell
curl -i -X GET https://api.appio.so/ios/ff \
-H "Authorization: Bearer prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP" \
-H "X-App-Platform: ios" \
-H "X-App-Version: 1.1"
```

Response:
```json
{
  "id": "wgt_00000000000000000000000000",
  "service_id": "svc_00000000000000000000000000",
  "config": "...",
}
```
