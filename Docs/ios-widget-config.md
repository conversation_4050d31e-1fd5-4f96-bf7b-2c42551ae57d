# Widget Template JSON Documentation

This documentation describes the widget elements supported by the Appio system and how to configure them using JSON. Each element maps to a SwiftUI view and has customizable properties.

## WidgetTemplate

Main object representing the widget template configuration.

| Property | Type | Description |
|----------|------|-------------|
| `variants` | `[WidgetVariant]` | Array of [widget variants](#widgetvariant) for different sizes |

## WidgetVariant

Represents a widget configuration for a specific widget families.

| Property | Type | Description |
|----------|------|-------------|
| `properties` | `WidgetVariantProperties` | [Variant-level properties](#widgetvariantproperties) |
| `elements` | `[WidgetElement]` | Array of [widget elements](#widgetelement) |

## WidgetVariantProperties

Properties that apply to the entire widget variant.

| Property | Type | Description |
|----------|------|-------------|
| `version` | `String?` | Configuration version |
| `supportedFamilies` | `[String]?` | Supported widget families: `systemSmall`, `systemMedium`, `systemLarge`, `systemExtraLarge`, `accessoryCircular`, `accessoryRectangular`, `accessoryInline` |
| `background` | `String?` | Background [Color](#color) |
| `url` | `String?` | The URL to open when clicked on a widget |

## WidgetElement

Main object representing widget element.

| Property | Type | Description |
|----------|------|-------------|
| `type` | `String` | [Widget Element Type](#widget-element-types) |
| `properties` | `Object` | Element properties. Each type has its own set of properties. |
| `elements` | `[WidgetElement]` | Nested elements |


---
## Widget Element Types

- [ellipse](#ellipse)
- [gauge](#gauge)
- [hstack / vstack / zstack](#hstack--vstack--zstack)
- [image](#image)
- [rectangle](#rectangle)
- [spacer](#spacer)
- [text](#text)

- [lastUpdated](#lastUpdated) - custom, non-native
- [refreshButton](#refreshButton) - custom, non-native

---
## ellipse

Renders an ellipse.

| Property | Type | Description |
|----------|------|-------------|
| `color` | `String?` | Fill [Color](#color) |
| `height` | `CGFloat?` | Height |
| `opacity` | `Double?` | Opacity |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `CGFloat?` | Width |

---
## gauge

Displays a SwiftUI `Gauge`.

| Property | Type | Description |
|----------|------|-------------|
| `color` | `String?` | [Color](#color) for labels |
| `currentValueLabel` | `String?` | Current value label |
| `label` | `String?` | Gauge label |
| `max` | `Double?` | Maximum value (default: `100`) |
| `maximumValueLabel` | `String?` | Maximum value label |
| `min` | `Double?` | Minimum value (default: `0`) |
| `minimumValueLabel` | `String?` | Minimum value label |
| `opacity` | `Double?` | Opacity |
| `padding` | `Padding?` | [Padding](#padding) |
| `scaleEffect` | `CGFloat?` | Visual scale multiplier |
| `style` | `String?` | Gauge style: `linearCapacity`, `accessoryCircular`, `accessoryLinear`, `accessoryCircularCapacity`, `accessoryLinearCapacity` |
| `tint` | `String?` | Tint [Color](#color) |
| `value` | `Double` | **required**: Current gauge value |

---
## hstack / vstack / zstack

Arranges child elements. Horizontal (`hstack`), vertical (`vstack`), or in depth (`zstack`).

| Property | Type | Description |
|----------|------|-------------|
| `alignment` | `String?` | Alignment depending on stack type: <br> └ **zstack**: `topLeading`, `top`, `topTrailing`, `leading`, `center`, `trailing`, `bottomLeading`, `bottom`, `bottomTrailing` <br> └ **hstack**: `top`, `center`, `bottom` <br> └ **vstack**: `leading`, `center`, `trailing` |
| `background` | `String?` | Background [Color](#color) |
| `spacing` | `CGFloat?` | Space between elements |
| `elements` | `[WidgetElement]` | Nested elements |

---
## image

Displays an image either from a URL or SF Symbol.

| Property | Type | Description |
|----------|------|-------------|
| `borderColor` | `String?` | Border [Color](#color) |
| `borderWidth` | `CGFloat?` | Border width |
| `contentMode` | `String?` | `fit` or `fill` (default: `fit`) |
| `cornerRadius` | `CGFloat?` | Corner radius |
| `height` | `CGFloat?` | Height |
| `opacity` | `Double?` | Opacity |
| `padding` | `Padding?` | [Padding](#padding) |
| `src` | `String?` | Remote image URL |
| `symbol` | `String?` | SF Symbol name |
| `width` | `CGFloat?` | Width |

---
## rectangle

Renders a customizable rectangle.

| Property | Type | Description |
|----------|------|-------------|
| `color` | `String?` | Fill [Color](#color) |
| `cornerRadius` | `CGFloat?` | Corner radius |
| `height` | `CGFloat?` | Height |
| `opacity` | `Double?` | Opacity |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `CGFloat?` | Width |

---
## spacer

Adds flexible spacing.

| Property | Type | Description |
|----------|------|-------------|
| `length` | `CGFloat?` | Minimum length |
| `padding` | `Padding?` | [Padding](#padding) |

---
## text

Displays a text label.

| Property | Type | Description |
|----------|------|-------------|
| `alignment` | `String?` | Multiline alignment: `leading`, `center`, `trailing` |
| `background` | `String?` | Background [Color](#color) |
| `color` | `String?` | Text [Color](#color) |
| `fontSize` | `CGFloat?` | Font size (default: 16) |
| `fontWeight` | `String?` | Font weight: `ultralight`, `thin`, `light`, `regular`, `medium`, `semibold`, `bold`, `heavy`, `black` |
| `italic` | `Bool?` | Italic text |
| `padding` | `Padding?` | [Padding](#padding) |
| `strikethrough` | `Bool?` | Strikethrough |
| `text` | `String` | **Required**: Text content
| `underline` | `Bool?` | Underline |

---
## refreshButton

Displays a button to trigger a widget refresh. This is a custom, non-native element.

| Property | Type | Description |
|----------|------|-------------|
| `color` | `String?` | Text [Color](#color) |
| `padding` | `Padding?` | [Padding](#padding) |
| `style` | `String?` | One of: `bordered`, `borderedProminent`, `plain`, `borderless` |
| `text` | `String?` | Label of the button (default: `Refresh`) |
| `tint` | `String?` | Tint [Color](#color) |

---
## lastUpdated

Displays the time of last data refresh. This is a custom, non-native element.

| Property | Type | Description |
|----------|------|-------------|
| `background` | `String?` | Background [Color](#color) |
| `color` | `String?` | Text [Color](#color) |
| `fontSize` | `CGFloat?` | Font size |
| `padding` | `Padding?` | [Padding](#padding) |

---

## Padding

```json
"padding": {
  "top": 4, 
  "bottom": 4,
  "left": 8,
  "right": 8
}
```

| Field | Type | Description |
|-------|------|-------------|
| `top`, `bottom`, `left`, `right` | `CGFloat?` | Edge-specific padding |


---

## Color

Can be any of the following named SwiftUI colors:

- `black`, `blue`, `brown`, `cyan`, `gray`, `green`, `indigo`, `mint`, `orange`, `pink`, `purple`, `red`, `teal`, `white`, `yellow`

Matching Android:
- `magenta`, `aqua`, `darkgray`, `darkgrey`, `fuchsia`, `grey`, `lightgray`, `lightgrey`, `lime`, `maroon`, `navy`, `olive`, `silver`

iOS specific:
- `accentColor`, `clear`, `primary`, `secondary`,

You may also use a hex value (e.g. `"#00FF00` or `"#F9a` or `#aa00FF` ) <br> Or even 2 colors for light/dark modes as comma separated string `{light},{dark}` (e.g. `"#000000,#FFFFFF"` or `#A0f,#f00`)
