# iOS Sizes

There is **no reliable way** to detect SmartBanner presence via JavaScript.

The size of SmartBanner is different based on:
- portrait/landscape
- ios/safari version (apple tweeks this)
- pixel density
- screen resolution

Values that change when SAP is present: `inner`, `documentElement`, `visualViewport`
Values that do not change: `screen`, `screenAvail`
Values get affected by: 
- Safari zoom-in/out
- Safari search bar on top/bottom
- Safari tabs in landscape mode
- website content (more content off the screen)

Smart App Banner appears with unknow delay on slow connections. Need testing.

https://demo.appio.so/__other/smart-banner-detection.html

```javascript
{
    time: Date.now(),
    screen: `${screen.width} x ${screen.height}`,
    screenAvail: `${screen.availWidth} x ${screen.availHeight}`,
    inner: `${window.innerWidth} x ${window.innerHeight}`,
    documentElement: `${document.documentElement.clientWidth} x ${document.documentElement.clientHeight}`,
    visualViewport: `${window.visualViewport.width} x ${window.visualViewport.height}`,
    webkit: !!(window.webkit && window.webkit.messageHandlers),
}
```


## iOS sizes (missing iPads and new iPhones)

iPhone 6 Plus portrait: 544px;
iPhone 6 Plus landscape: 286px;
iPhone 6 portrait: 475px;
iPhone 6 landscape: 247px;
iPhone 5 & 5S portrait: 376px;
iPhone 5 & 5S landscape: 148px;
iPhone 2G, 3G, 4, 4S, iPod Touch generations 1-4 portrait: 288px;
iPhone 2G, 3G, 4, 4S, iPod Touch generations 1-4 landscape: 148px;



