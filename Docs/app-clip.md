# App Clip

## Flow
App Clip overlay is not triggered automatically. User has to click on the button in Smart Banner to launch it.
Adds 2 extra steps to the process:
- click View on Smart Banner to launch App Clip Overlay
- click on View on App Clip Overlay to launch App Clip


## How to test?
No app, appClip should be installed. Delete both.
Smart banner will appear.
Clicking on smart banne will trigger app clip with a button to launch appClip.
If this doesn't work:
Restart phone.
Setup local experiences in Settings > Developer
Clear experience cache
Install appClip and test via smart banner. Delete app clip and test smart banner again. Restart.

## Setup
- image: 1800x1200 px PNG or JPEG
- title: 30 chars (18)
- subtitle: 56 chars (43)
- CTA verb
 
Generate:
AppClipCodeGenerator generate --url https://app.appio.so/?s=svc_00000000000000000000000000&u=user123 --logo=none --foreground FF5500 --background FEF3DE  --output ~/Downloads/appio.june.so.svg

Link to AppClip in Apple Store:
https://appclip.apple.com/id?p=so.appio.app.Clip&key=value

