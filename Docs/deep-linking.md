# Deep linking services

## Linking from iOS to iOS

Linking to Safari link assocaited with an app from an app, will auto-open associated app, and not stay in Safari.

Safari banner -> Download app -> Open app -> (fingerprint PRESENT but fails) ...
-> App open default https://app.appio.so in Safari + with query string identifying that this request was triggered by the app (add trigger timestamp, security?)
-> indexedDB + Local Storage in Safari picks up service details (validation needed? no)
-> JS open custom scheme URL appio://data (this is safe as I identified requested opened via app)
   iOS shows confirmation before opening custom URL scheme.
   defaults to show page with "Complete setup" button that would link to custom scheme

Edge cases:
- no fingerprint found (or too old?) in app - scan QR code in app 
- indexedDB + Local storage is empty (private browsing, user deleted data) - ask user to visit service page? (need to redirect to initial step)
- user declines oepning custom URL scheme - show page with "Complete setup" button

Risks:
- leaking data stored in indexedDB + Local storage - encrypt with expiration? server token?


## Services

- Firebase Dynamic Links: Deprecation 2025-08-25 https://firebase.google.com/support/dynamic-links-faq
- Adjust.com
- Airbridge: https://www.airbridge.io/glossary/deferred-deep-linking
- AppsFlyer: https://www.appsflyer.com/glossary/deferred-deep-linking/
- Bitly
- Branch.io
- Kochava
- Singular: https://www.singular.net/deep-linking/


## Adjust Example
Example of custom banner:
https://www.zillow.com/homes/for_rent/
Banner links to: (most likely old link)
https://r3fc.us.adj.st/universallink/https://www.zillow.com/homes/for_rent/?requestid=e831cd5e-b5d9-4858-baa3-b367f94d613f&adj_t=rw2qau8_ra7mjoq
redirects to: (seems like a new format)
https://r3fc.us.adj.st/sulink/universallink/https:/www.zillow.com/homes/for_rent/?requestid=7b906645-8e81-422a-82c2-fc52f1ce30a4&adj_t=rw2qau8_ra7mjoq
- redirects to app store if app is not installed, no leftover tab stays open
- if app is installed it opens the app directly, returning to Safari shows leftover "Open in Zillow?" popup
- inconsistnet behaviour. clicking on "Open" on Adjust banner redirects to App Store even thought the app is installed.
- once the app installed, double banner is shown: Adjust + Apple
- some cookie storing happening ?
- it seems they are doing Javascript timing to try to open custome scheme and otherwise redirecting to custom page urging to download an app
  - also doing some UserAgent scanning as this page doesn't show in desktop Chrome
  - this page contains simple JS: window.location.href = 'https://apps.apple.com/app/id310738695?mt=8';
 
  


