//
//  NotificationService.swift
//  AppioNotificationServiceExtension
//
//  Created by gondo on 12/03/2025.
//

import UserNotifications

// Process foreground notification locally before it is displayed to user
// Used to download and show service logo on a notification
class NotificationService: UNNotificationServiceExtension {
    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    // NOTE: only triggered for notifications with "mutable-content": 1,
    override func didReceive(_ request: UNNotificationRequest,
                             withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void)
    {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

        print("Received notification in Notification Service Extension")

        guard let bestAttemptContent = bestAttemptContent,
              let data = request.content.userInfo["data"] as? [String: Any],
              let _ = data["notification_id"] as? String,
              let _ = data["service_id"] as? String,
              let imageURLString = data["logo"] as? String,
              let imageURL = URL(string: imageURLString)
        else {
            print("Received notification with invalid data. notification_id, service_id, logo are required")
            contentHandler(request.content)
            return
        }

        NotificationsStorer.storeNotification(content: bestAttemptContent)
        notifyMainApp()

        // show service logo on notification
        downloadImage(url: imageURL) { attachment in
            if let attachment = attachment {
                bestAttemptContent.attachments = [attachment]
            }
            contentHandler(bestAttemptContent)
        }
    }

    // works only for app in foreground and only if long pressed the notification
    private func notifyMainApp() {
        // can't pass any data. just signal
        let notificationName = CFNotificationName("so.appio.app.notification-service-extension" as CFString)
        CFNotificationCenterPostNotification(CFNotificationCenterGetDarwinNotifyCenter(),
                                             notificationName,
                                             nil,
                                             nil,
                                             true)
    }

    /// Called just before the extension will be terminated by the system.
    override func serviceExtensionTimeWillExpire() {
        if let contentHandler = contentHandler, let bestAttemptContent = bestAttemptContent {
            // place custom code here

            contentHandler(bestAttemptContent)
        }
    }

    // N2H: refactore to use ImageManager (app group + getCacheFileName)
    private func downloadImage(url: URL, completion: @escaping (UNNotificationAttachment?) -> Void) {
        URLSession.shared.downloadTask(with: url) { tempLocation, response, _ in
            guard let tempLocation = tempLocation,
                  let httpResponse = response as? HTTPURLResponse,
                  let mimeType = httpResponse.mimeType
            else {
                completion(nil)
                return
            }

            let fileManager = FileManager.default
            let fileExtension: String

            switch mimeType {
            case "image/jpeg": fileExtension = "jpg"
            case "image/png": fileExtension = "png"
            case "image/gif": fileExtension = "gif"
            case "image/heic": fileExtension = "heic"
            case "image/bmp": fileExtension = "bmp"
            case "image/webp": fileExtension = "webp"
            default:
                print("Unsupported MIME type: \(mimeType)")
                completion(nil)
                return
            }

            let attachmentURL = tempLocation.deletingPathExtension().appendingPathExtension(fileExtension)

            do {
                try fileManager.moveItem(at: tempLocation, to: attachmentURL)
//                // both of these prevents preview to show on notification on iphone and watch
//                let options: [NSObject: AnyObject] = [
//                    UNNotificationAttachmentOptionsThumbnailHiddenKey as NSObject: true as AnyObject, // prevent preview to show on apple watch. doesn't work
//                    UNNotificationAttachmentOptionsTypeHintKey as NSString: attachmentURL.lastPathComponent as NSString,
//                ]
                let attachment = try UNNotificationAttachment(identifier: url.absoluteString, url: attachmentURL, options: nil)
                completion(attachment)
            } catch {
                print("Failed to create attachment: \(error)")
                completion(nil)
            }
        }.resume()
    }
}
