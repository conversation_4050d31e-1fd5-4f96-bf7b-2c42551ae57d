//
//  NotificationViewController.swift
//  AppioNotificationContentExtension
//
//  Created by gondo on 12/03/2025.
//

import UIKit
import UserNotifications
import UserNotificationsUI

// Used to display notification image
// Gets executed only for notifications with category defined in AppioNotificationContentExtension/Info.plist
// Long press on notification
// Sometimes it takes ages to see print messages and the whole MainInterface.storyboard to load (including background and image)
// Keep waiting after long press
class NotificationViewController: UIViewController, UNNotificationContentExtension {
    @IBOutlet var imageView: UIImageView?

    override func viewDidLoad() {
        super.viewDidLoad()
        Log.shared.warning("📌 Notification Content Extension Loaded")

        // setFallbackImage() // no need, starting with invisible image and expanding if/once image loaded
    }

    func didReceive(_ notification: UNNotification) {
        Log.shared.warning("Received notification in Notification Content Extension")
        let content = notification.request.content
        guard let data = content.userInfo["data"] as? [String: Any],
              let imageURLString = data["image_url"] as? String,
              let imageURL = URL(string: imageURLString)
        else {
            return
        }

        // Set proper height based on image
        let height = UIScreen.main.bounds.width / 16 * 9
        preferredContentSize = CGSize(width: view.bounds.width, height: height)

        // Make image clickable
        imageView?.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(imageTapped))
        imageView?.addGestureRecognizer(tapGesture)

        Log.shared.warning("loading image: \(imageURL)")
        loadImage(from: imageURL)
    }

    @objc func imageTapped() {
        Log.shared.warning("Tapped on notification image")
        extensionContext?.performNotificationDefaultAction()
    }

    private func setFallbackImage() {
        let fallbackImage = UIImage(systemName: "photo")?
            .withConfiguration(UIImage.SymbolConfiguration(pointSize: 100, weight: .regular))

        imageView?.tintColor = .systemGray
        imageView?.alpha = 0.5
        imageView?.contentMode = .center
        imageView?.image = fallbackImage
    }

    private func loadImage(from url: URL) {
        URLSession.shared.dataTask(with: url) { [weak self] data, _, _ in
            guard let self = self,
                  let data = data,
                  let image = UIImage(data: data)
            else {
                return
            }

            DispatchQueue.main.async {
                Log.shared.warning("displaying notification image")
                self.imageView?.alpha = 1
                self.imageView?.contentMode = .scaleAspectFill
                self.imageView?.image = image
            }
        }.resume()
    }
}
