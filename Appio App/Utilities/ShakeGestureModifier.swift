//
//  ShakeGestureModifier.swift
//  Appio
//
//  Created by gondo on 18/03/2025.
//

import SwiftUI
import UIKit

// Modifier to detect shake gesture in SwiftUI views
struct DeviceShakeViewModifier: ViewModifier {
    let action: () -> Void

    func body(content: Content) -> some View {
        content
            .onAppear()
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.deviceDidShakeNotification)) { _ in
                action()
            }
    }
}

// Notification to detect shake
extension UIDevice {
    static let deviceDidShakeNotification = NSNotification.Name(rawValue: "deviceDidShakeNotification")

    // Needed for preview debugging
    static func simulateShake() {
        NotificationCenter.default.post(name: deviceDidShakeNotification, object: nil)
    }
}

// Window detection for shake gesture
extension UIWindow {
    override open func motionEnded(_ motion: UIEvent.EventSubtype, with _: UIEvent?) {
        if motion == .motionShake {
            NotificationCenter.default.post(name: UIDevice.deviceDidShakeNotification, object: nil)
        }
    }
}

// View extension for easy usage
extension View {
    func onShake(perform action: @escaping () -> Void) -> some View {
        modifier(DeviceShakeViewModifier(action: action))
    }
}

#if DEBUG

    // Preview structure with state management
    struct ShakePreviewView: View {
        @State private var shaken = false

        var body: some View {
            VStack(spacing: 48) {
                Text(shaken ? "Shaken!" : "Not shaken")
                    .font(.title)
                    .onShake {
                        shaken = true
                    }

                Button("Simulate Shake") {
                    UIDevice.simulateShake()
                }
                .buttonStyle(.borderedProminent)

                Button("Reset") {
                    shaken = false
                }
                .buttonStyle(.bordered)
            }
        }
    }

    #Preview {
        ShakePreviewView()
    }

#endif
