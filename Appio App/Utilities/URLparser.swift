//
//  URLparser.swift
//  Appio
//
//  Created by gondo on 07/03/2025.
//

import Foundation

protocol URLparserProtocol {
    static func parseParams(from url: URL) throws -> URLparams
}

struct URLparams {
    let serviceId: String
    let customerUserId: String?
}

// only relevant to service init url
enum URLparser: URLparserProtocol {
    static func parseParams(from url: URL) throws -> URLparams {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems
        else {
            throw URLerror.invalidFormat
        }

        if url.scheme == "appio" {
            return try parseQueryString(queryItems: queryItems, serviceKey: "service", userKey: "user")
        }
        if url.scheme == "https" && url.host == "app.appio.so" {
            return try parseQueryString(queryItems: queryItems, serviceKey: "s", userKey: "u")
        }

        throw URLerror.invalidFormat
    }

    private static func parseQueryString(queryItems: [URLQueryItem], serviceKey: String, userKey: String) throws -> URLparams {
        guard let serviceParam = queryItems.first(where: { $0.name == serviceKey }),
              let serviceId = serviceParam.value, !serviceId.isEmpty
        else {
            throw URLerror.invalidFormat
        }

        let userParam = queryItems.first(where: { $0.name == userKey })
        return URLparams(serviceId: serviceId, customerUserId: userParam?.value)
    }
}

enum URLerror: Error, LocalizedError {
    case invalidFormat

    var errorDescription: String? {
        switch self {
        case .invalidFormat:
            return "Invalid URL format. Expected: appio://appio/?service=X&user=Y or https://app.appio.so/?s=X&u=Y"
        }
    }
}
