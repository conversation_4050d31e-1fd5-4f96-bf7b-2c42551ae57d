//
//  --SlimeProgressDotPageIndicator.swift
//  Appio
//
//  Created by gondo on 10/03/2025.
//

import SwiftUI

struct SlimeProgressDotPageIndicator: View {
    private let currentPage: Int
    private let numberOfPages: Int
    private let hidesForSinglePage: Bool
    private let config: Config
    private let progress: CGFloat

    private var adjustedIndex: Int {
        return currentPage < 0 ? numberOfPages : (currentPage > numberOfPages ? 0 : currentPage)
    }

    struct Config {
        var dotSize: CGFloat = 8
        var pageIndicatorHighlight: Color = .secondary
        var pageIndicatorNext: Color = .secondary
        var pageIndicatorLast: Color = .secondary
    }

    init(currentPage: Int, numberOfPages: Int, progress: CGFloat, hidesForSinglePage: Bool = true, config: Config = Config()) {
        self.currentPage = currentPage
        self.numberOfPages = numberOfPages - 1
        self.progress = min(max(progress, 0), 1)
        self.hidesForSinglePage = hidesForSinglePage
        self.config = config
    }

    var body: some View {
        HStack(spacing: 10) {
            ForEach(Array(0 ..< numberOfPages + 1), id: \.self) { index in
                Capsule()
                    .fill(index == adjustedIndex ? config.pageIndicatorHighlight : index < currentPage ? config.pageIndicatorLast : config.pageIndicatorNext)
                    .frame(width: index == currentPage ? config.dotSize * 2.5 : config.dotSize, height: config.dotSize)
                    .transition(.slide)
                    .animation(.easeInOut, value: currentPage)
                    .overlay(alignment: .leading) {
                        if index == currentPage && progress != 0 {
                            Capsule()
                                .frame(width: index == currentPage ? (config.dotSize * 2.5) * progress : config.dotSize, height: config.dotSize)
                                .animation(.linear(duration: 1), value: progress)
                        }
                    }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(.thickMaterial, in: .capsule)
        .opacity(numberOfPages == 0 && hidesForSinglePage ? 0 : 1)
    }
}

#Preview {
    VStack {
        SlimeProgressDotPageIndicator(currentPage: 1, numberOfPages: 5, progress: 0.5)
        SlimeProgressDotPageIndicator(currentPage: 2, numberOfPages: 5, progress: 0.7)
        SlimeProgressDotPageIndicator(currentPage: 3, numberOfPages: 5, progress: 1)
    }
}
