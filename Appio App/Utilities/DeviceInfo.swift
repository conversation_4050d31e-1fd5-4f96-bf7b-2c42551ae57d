//
//  DeviceInfo.swift
//  Appio
//
//  Created by gondo on 05/03/2025.
//

import SwiftUI
import UIKit
import WebKit

struct DeviceInfo {
    var model: String
    var osVersion: String
    var screenResolution: String
    var pixelDensity: CGFloat
    var timeZone: String
    var timeOffset: Int
    var language: String
    var userAgent: String
}

enum DeviceInfoProvider {
    @MainActor
    static func getDeviceInfo() async -> DeviceInfo {
        let screen = UIScreen.main
        let screenResolution = "\(Int(screen.bounds.width))x\(Int(screen.bounds.height))"

        let userAgent = (try? await getUserAgent()) ?? "Unknown"

        return DeviceInfo(
            model: UIDevice.current.model,
            osVersion: UIDevice.current.systemVersion,
            screenResolution: screenResolution,
            pixelDensity: screen.scale,
            timeZone: TimeZone.current.identifier,
            timeOffset: TimeZone.current.secondsFromGMT() / -60, // transform seconds to expected negative minutes 
            language: Locale.preferredLanguages.first ?? "Unknown",
            userAgent: userAgent
        )
    }

    /**
        WARNING: User-agent from app is slightly different vs Safari:
        SAF: Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1
        APP: Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148
        Server is normalising this data to perform matching
     */
    @MainActor
    private static func getUserAgent() async throws -> String {
        let webView = WKWebView(frame: .zero)

        try await withCheckedThrowingContinuation { continuation in
            let delegate = WebViewDelegate {
                Log.shared.warning("WebView completed loading")
                continuation.resume()
            }
            webView.navigationDelegate = delegate
            webView.loadHTMLString("<html></html>", baseURL: nil)

            // Keep delegate alive until finished
            objc_setAssociatedObject(webView, "delegate", delegate, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }

        return try await webView.evaluateJavaScript("navigator.userAgent") as? String ?? "Unknown"
    }
}

private class WebViewDelegate: NSObject, WKNavigationDelegate {
    let onFinish: () -> Void

    init(onFinish: @escaping () -> Void) {
        self.onFinish = onFinish
    }

    func webView(_: WKWebView, didFinish _: WKNavigation!) {
        onFinish()
    }
}
