//
//  DeepLinkCoordinator.swift
//  Appio
//
//  Created by gondo on 12/09/2025.
//

import SwiftUI

@MainActor
final class DeepLinkCoordinator {
    static let shared = DeepLinkCoordinator()

    private var nextAllowedAt: Date = .distantPast
    private let gateWindow: TimeInterval = 1.0

    func handle(url: URL, appViewModel: AppViewModel) async {
        let now = Date()
        if now < nextAllowedAt {
            Log.shared.warning("Deep link dropped by gate: \(url)")
            return
        }
        nextAllowedAt = now.addingTimeInterval(gateWindow)

        Log.shared.warning("App opened with url: \(url)")

        // appio://appio/...
        if url.scheme == "appio" && url.host == "appio" && url.pathComponents.count >= 2 {
            switch url.pathComponents[1] {
            case "widget":
                // Open service view, can be followed by other action
                if let serviceId = url.getQueryItem(key: "serviceId") {
                    await appViewModel.start()
                    appViewModel.navigateToService(serviceId: serviceId)
                }

                // Open external link
                if let valueUrl = url.getQueryItem(key: "url"),
                   let link = URL(string: valueUrl),
                   UIApplication.shared.canOpenURL(link)
                {
                    await UIApplication.shared.open(link)

                    return
                }

                // End this action
                return

//                // N2H: general settings page to configure notifications per service. Linked from App's system settings page paragraph defined in Settings.bundle
//                case "settings":
//                    print("show notifications Settings page")
//
//                // N2H: handle control widget launch
//                case "control-widget":
//                    Log.shared.warning("Control Widget launch")
//                    await appViewModel.start()

            default:
                // appio://appio
                return
            }
        }

        // appio://appio/?service=X&user=Y or https://app.appio.so/?s=X&u=Y
        await appViewModel.start(with: url)
    }
}
