//
//  AnyDecodable.swift
//  Appio
//
//  Created by gondo on 10/03/2025.
//

struct AnyDecodable: Decodable {
    let value: Any

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        if let intVal = try? container.decode(Int.self) {
            value = intVal
        } else if let doubleVal = try? container.decode(Double.self) {
            value = doubleVal
        } else if let boolVal = try? container.decode(Bool.self) {
            value = boolVal
        } else if let stringVal = try? container.decode(String.self) {
            value = stringVal
        } else if let arrayVal = try? container.decode([AnyDecodable].self) {
            value = arrayVal.map { $0.value }
        } else if let dictVal = try? container.decode([String: AnyDecodable].self) {
            var dict: [String: Any] = [:]
            for (key, value) in dictVal {
                dict[key] = value.value
            }
            value = dict
        } else {
            throw DecodingError.dataCorruptedError(in: container,
                                                   debugDescription: "Unable to decode value")
        }
    }
}
