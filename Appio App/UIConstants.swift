//
//  UIConstants.swift
//  Appio App
//
//  Created by gondo on 22/10/2024.
//

import SwiftUI

enum UIConstants {
    // MARK: - Spacing

    static let spacing: CGFloat = 16
    static let smallSpacing: CGFloat = 8
    static let largeSpacing: CGFloat = 24

    // MARK: - Corner Radius

    static let logoCornerRadius: CGFloat = 10
    static let buttonCornerRadius: CGFloat = 12
    static let smallCellCornerRadius: CGFloat = 12
    static let bigCellCornerRadius: CGFloat = 24
    static let sheetCornerRadius: CGFloat = 40

//    // MARK: - Animation
//    static let defaultAnimation: Animation = .easeInOut(duration: 0.3)
//

    // MARK: - Layout

    static let logoSize: CGFloat = 50
//    static let maxWidth: CGFloat = 500
//    static let minTapTarget: CGFloat = 44
}
