//
//  ApiError.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import SwiftUI

enum ApiError: Error, LocalizedError {
    case invalidURL
    case badServerResponse
    case networkTimeout
    case networkError(Error)
    case unexpectedStatusCode(Int, String)
    case failedToDecode(String)
    case debounce

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL."
        case .badServerResponse:
            return "Bad server response."
        case .networkTimeout:
            return "Network timeout."
        case let .networkError(error):
            return "Network error: \(error.localizedDescription)"
        case let .unexpectedStatusCode(code, message):
            return "Unexpected status code \(code): \(message)"
        case let .failedToDecode(data):
            return "Failed to decode response: \(data)"
        case .debounce:
            return "Request debounced."
        }
    }
}
