//
//  APIServiceStruct.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

struct LinkServiceRequest: Codable {
    let customerUserId: String

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case customerUserId = "customer_user_id"
    }
}

struct ServiceResponse: Codable {
    let id: String
    let title: String
    let description: String?
    let logoURL: String
    let bannerURL: String?
    let URL: String?
    let widgets: [WidgetResponse]?

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case description
        case logoURL = "logo_url"
        case bannerURL = "banner_url"
        case URL = "url"
        case widgets
    }
}
