//
//  APINotification.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

// no status as all the notificatiosn on ios are delivered
struct NotificationResponse: Codable {
    let id: String
    let serviceId: String
    let payload: NotificationEntityPayloadResponse

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id
        case serviceId = "service_id"
        case payload
    }
}

struct NotificationEntityPayloadResponse: Codable {
    let title: String
    let subtitle: String
    let message: String
    var link: String?
    var imageUrl: String?

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case title, subtitle, message, link, imageUrl = "image_url"
    }
}
