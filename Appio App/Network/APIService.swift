//
//  APIService.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import SwiftUI

actor APIService {
    static let shared = APIService()
    private var debounce: [String: Date] = [:]
    private let minimumInterval: TimeInterval = 2.0

    private init() {} // singleton

    private func checkDebounce(for function: String, params: String...) throws {
        let key = ([function] + params).joined(separator: "-")
        if let lastCall = debounce[key],
           Date().timeIntervalSince(lastCall) < minimumInterval
        {
            Log.shared.warning("Debounce: \(key)")
            throw ApiError.debounce
        }
        debounce[key] = Date()
    }

    // Fingerprint: Match
    func fingerprintMatch(fingerprintRequest: FingerprintRequest) async throws -> FingerprintResponse? {
        try checkDebounce(for: #function)

        let data = try JSONEncoder().encode(fingerprintRequest)
        return try await APIClient.performRequest(endpoint: "/fingerprints/match",
                                                  method: "POST",
                                                  body: data)
    }

    // Device: Register a new device when none exists. Will start with notificationsEnable=false and ApnToken=""
    func registerDevice(serviceId: String, customerUserId: String) async throws -> DeviceResponse {
        try checkDebounce(for: #function)

        let request = NewDeviceRequest(
            customerUserId: customerUserId,
            model: await UIDevice.current.userInterInterface
        )
        let data = try JSONEncoder().encode(request)
        guard let response: DeviceResponse = try await APIClient.performRequest(endpoint: "/devices",
                                                                                method: "POST",
                                                                                headers: ["X-Service-Id": serviceId],
                                                                                body: data,
                                                                                expectedStatusCode: 201)
        else {
            // This shouldn't happen for a 201 response, but we need to handle it
            throw ApiError.badServerResponse
        }
        return response
    }

    // Device: Update
    func updateDevice(device: DeviceEntity) async throws -> Bool {
        // NOTE: not doing debounce because we want the latest update
        let request = UpdateDeviceRequest(
            notificationsEnabled: device.notifiationsEnabled,
            deviceToken: device.apnToken,
        )
        let data = try JSONEncoder().encode(request)
        let response = try await APIClient.performRequest(endpoint: "/devices/\(device.id)",
                                               method: "PATCH",
                                               body: data) as AnyDecodable?
        if response == nil {
            return false
        }
        return true
    }

    // Device: Link an existing device with a new service.
    func linkService(deviceId: String, serviceId: String, customerUserId: String) async throws -> Bool {
        try checkDebounce(for: #function, params: deviceId, serviceId, customerUserId)

        let request = LinkServiceRequest(customerUserId: customerUserId)
        let data = try JSONEncoder().encode(request)
        let response = try await APIClient.performRequest(endpoint: "/devices/\(deviceId)/services",
                                               method: "POST",
                                               headers: ["X-Service-Id": serviceId],
                                               body: data) as AnyDecodable?
        if response == nil {
            return false
        }
        return true
    }

    // Device: Detach Device from Service
    func detachService(serviceId: String, deviceId: String) async throws -> Bool {
        try checkDebounce(for: #function, params: deviceId, serviceId)

        let response = try await APIClient.performRequest(endpoint: "/devices/\(deviceId)",
                                               method: "DELETE",
                                               headers: ["X-Service-Id": serviceId]) as AnyDecodable?
        if response == nil {
            return false
        }
        return true
    }

    // Widget: Fetch single widget
    func fetchWidget(serviceId: String, widgetId: String) async throws -> WidgetResponse {
        try checkDebounce(for: #function, params: serviceId, widgetId)

        guard let response: WidgetResponse = try await APIClient.performRequest(endpoint: "/widgets/\(widgetId)",
                                                                                method: "GET",
                                                                                headers: ["X-Service-Id": serviceId])
        else {
            throw ApiError.badServerResponse
        }
        return response
    }

    // Service: Fetch single service details
    func fetchService(id serviceId: String) async throws -> ServiceResponse {
        try checkDebounce(for: #function, params: serviceId)

        guard let response: ServiceResponse = try await APIClient.performRequest(endpoint: "/services/\(serviceId)",
                                                                                 method: "GET",
                                                                                 headers: ["X-Service-Id": serviceId])
        else {
            throw ApiError.badServerResponse
        }
        return response
    }

    // Service: List all for Device
    func fetchAllServices(deviceId: String) async throws -> [ServiceResponse] {
        try checkDebounce(for: #function, params: deviceId)

        guard let list: [ServiceResponse] = try await APIClient.performRequest(endpoint: "/services",
                                                                               method: "GET",
                                                                               headers: ["X-Device-Id": deviceId])
        else {
            return [] // Return empty array if nil
        }
        return list
    }

    // Notification: List all for Service, Device
    func fetchAllNotifications(serviceId: String, deviceId: String, cursor: String) async throws -> [NotificationResponse] {
        try checkDebounce(for: #function, params: serviceId, deviceId)

        guard let list: [NotificationResponse] = try await APIClient.performRequest(endpoint: "/notifications?cursor=\(cursor)",
                                                                                    method: "GET",
                                                                                    headers: ["X-Service-Id": serviceId,
                                                                                              "X-Device-Id": deviceId])
        else {
            return [] // Return empty array if nil
        }
        return list
    }

    // Feature Flags: get all for this app version
    func fetchFeatureFlags() async throws -> FeatureFlagsEntity? {
        try checkDebounce(for: #function)

        guard let response: FeatureFlagsEntity = try await APIClient.performRequest(endpoint: "/ff", method: "GET") else {
            throw ApiError.badServerResponse
        }
        return response
    }
    
    func submitFeedback(serviceId: String, deviceId: String, message: String) async throws -> Bool {
        try checkDebounce(for: #function, params: deviceId, serviceId, message)

        let request = FeedbackRequest(message: message)
        let data = try JSONEncoder().encode(request)
        let response = try await APIClient.performRequest(endpoint: "/feedback",
                                               method: "POST",
                                               headers: ["X-Service-Id": serviceId,
                                                         "X-Device-Id": deviceId],
                                               body: data,
                                               expectedStatusCode: 201) as AnyDecodable?
        if response == nil {
            return false
        }
        return true
    }
}
