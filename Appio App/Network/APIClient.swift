//
//  APIClient.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation
import UIKit

enum APIClient {
    private static let baseURL = "https://api.appio.so/ios"
    private static let authToken = "prod_4hzHWQf8z7iOJDk9trZ8NyYcr8Ys7osXXhkSZMngJnzv5vMQAP"
    private static let timeout: TimeInterval = 10.0 // seconds

    // Generic request method to reduce duplication
    static func performRequest<T: Decodable>(endpoint: String,
                                             method: String,
                                             headers: [String: String] = [:],
                                             body: Data? = nil,
                                             expectedStatusCode: Int = 200) async throws -> T?
    {
        guard let url = URL(string: "\(baseURL)\(endpoint)") else {
            throw ApiError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.timeoutInterval = timeout
        request.httpBody = body

        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Diagnostic data
        request.setValue("ios", forHTTPHeaderField: "X-App-Platform")
        request.setValue(AppVersion.current(), forHTTPHeaderField: "X-App-Version")
        await request.setValue(UIDevice.current.systemVersion, forHTTPHeaderField: "X-System-Version")
        await request.setValue(UIDevice.current.deviceIdentifier, forHTTPHeaderField: "X-Device-Identifier")
        await request.setValue(String(reflecting: UIDevice.current.screenSize), forHTTPHeaderField: "X-Screen-Size")

        // Add custom headers
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }

        do {
            let (responseData, response) = try await URLSession.shared.data(for: request)
            guard let httpResponse = response as? HTTPURLResponse else {
                throw ApiError.badServerResponse
            }

            // DEBUG API
            print("---------- <API>")
            print("API method: \(method)")
            print("API request url: \(url)")
            print(request.allHTTPHeaderFields!.map { "API request Header: \($0): \($1)" }.joined(separator: "\n"))
            print("API request body: \(String(data: body ?? Data(), encoding: .utf8)!)")
            print("API response code: \(httpResponse.statusCode)")
            print("API response body: \(String(data: responseData, encoding: .utf8)!)")
            print("---------- </API>")

            // For 404, we'll return nil (common pattern for "not found" resources)
            if httpResponse.statusCode == 404 {
                return nil
            }

            guard httpResponse.statusCode == expectedStatusCode else {
                let errMsg = String(data: responseData, encoding: .utf8) ?? "Unknown error"
                throw ApiError.unexpectedStatusCode(httpResponse.statusCode, errMsg)
            }

            do {
                return try JSONDecoder().decode(T.self, from: responseData)
            } catch let error as DecodingError {
                switch error {
                case let .dataCorrupted(context):
                    print("Data corrupted:", context.debugDescription)
                case let .keyNotFound(key, context):
                    print("Key '\(key)' not found:", context.debugDescription)
                case let .typeMismatch(type, context):
                    print("Type mismatch for type \(type):", context.debugDescription)
                case let .valueNotFound(value, context):
                    print("Value '\(value)' not found:", context.debugDescription)
                @unknown default:
                    print("unkown")
                }
                throw error
            } catch {
                let data = String(data: responseData, encoding: .utf8) ?? ""
                throw ApiError.failedToDecode(data)
            }
        } catch let urlError as URLError {
            if urlError.code == .timedOut {
                throw ApiError.networkTimeout
            } else {
                throw ApiError.networkError(urlError)
            }
        } catch let apiError as ApiError {
            throw apiError
        } catch {
            throw ApiError.networkError(error)
        }
    }
}
