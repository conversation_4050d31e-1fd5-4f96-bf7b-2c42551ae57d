<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="23788" systemVersion="24D81" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithSwiftData="YES" userDefinedModelVersionIdentifier="">
    <entity name="Device" representedClassName="Device" syncable="YES" codeGenerationType="class">
        <attribute name="apnToken" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="lastApiSync" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastUpdate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="notifiationsEnabled" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    </entity>
    <entity name="Service" representedClassName="Service" syncable="YES" codeGenerationType="class">
        <attribute name="bannerURL" optional="YES" attributeType="String"/>
        <attribute name="desc" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="logoURL" attributeType="String"/>
        <attribute name="showPreview" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="title" attributeType="String"/>
    </entity>
</model>