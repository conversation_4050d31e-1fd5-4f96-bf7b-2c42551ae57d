//
//  DeviceSyncManager.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import Combine
import SwiftUI

class DeviceSyncManager {
    private var cancellable: AnyCancellable?

    init() {
        cancellable = StorageManager.devicePublisher
            .dropFirst() // Optionally ignore the initial state
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main) // Wait for changes to settle
            .filter { $0?.isOutOfSync() ?? false }
            .sink { device in
                guard var device = device else { return }
                Task {
                    print("Updating device: \(device)")
                    let ok = try await APIService.shared.updateDevice(device: device)
                    if ok {
                        device.markSynced()
                        StorageManager.device = device // store marked synced
                    }
                }
            }
    }

    deinit {
        cancellable?.cancel()
    }
}
