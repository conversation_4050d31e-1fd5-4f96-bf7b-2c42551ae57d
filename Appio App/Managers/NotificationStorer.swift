//
//  NotificationStorer.swift
//  Appio
//
//  Created by gondo on 03/04/2025.
//

import SwiftUI

enum NotificationsStorer {
    static func storeNotification(content: UNNotificationContent) {
        guard let data = content.userInfo["data"] as? [String: Any],
              let serviceId = data["service_id"] as? String,
              let notificationId = data["notification_id"] as? String
        else {
            return
        }

        let imageUrl = data["image_url"] as? String
        let link = data["link"] as? String

        // persist notification, only if it doesn't exist
        let ntfPayload = NotificationEntityPayloadResponse(title: content.title, subtitle: content.subtitle, message: content.body, link: link, imageUrl: imageUrl)
        let ntfResponse = NotificationResponse(id: notificationId, serviceId: serviceId, payload: ntfPayload)
        let notification = NotificationEntity(from: ntfResponse)
        StorageManager.appendNotification(notification: notification, to: serviceId)
    }
}
