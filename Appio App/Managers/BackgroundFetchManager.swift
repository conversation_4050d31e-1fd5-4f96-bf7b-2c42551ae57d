//
//  BackgroundFetchManager.swift
//  Appio
//
//  Created by gondo on 11/03/2025.
//

import BackgroundTasks
import UIKit

enum BackgroundFetchManager {
    static let identifier: String = "so.appio.app.refresh" // also defined in Appio-App-Info.plist

    @MainActor
    static func handleAppRefresh(task: BGAppRefreshTask) {
        // Schedule the next refresh
        scheduleAppRefresh()

        // Create a task to fetch data
        task.expirationHandler = {
            // Cancel any ongoing work if the task expires
            task.setTaskCompleted(success: false)
        }

        // Perform your data fetch here
        refreshData { success in
            task.setTaskCompleted(success: success)
        }
    }

    // task registered in AppDelegate via BackgroundFetchManager.identifier
    @MainActor
    static func scheduleAppRefresh() {
        guard isBackgroundRefreshEnabled() else {
            print("Background refresh is not available: \(UIApplication.shared.backgroundRefreshStatus.rawValue)")
            return
        }

        BGTaskScheduler.shared.getPendingTaskRequests { requests in
            let alreadyScheduled = requests.contains { $0.identifier == BackgroundFetchManager.identifier }
            if alreadyScheduled {
                print("ℹ️ Background task already scheduled, skipping.")
                return
            }

            let request = BGAppRefreshTaskRequest(identifier: BackgroundFetchManager.identifier)
            request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // Refresh after 15 minutes minimum

            do {
                try BGTaskScheduler.shared.submit(request)
                print("✅ Scheduled BG refresh for: \(request.earliestBeginDate!)")
            } catch {
                print("❌ Could not schedule app refresh: \(error)")
            }
        }
    }

    static func refreshData(completion: @escaping (Bool) -> Void) {
        Log.shared.warning("Refreshing data")

        Task.detached {
            await PushNotificationManager.refreshNotificationToken() // this will trigger device update if APN token changed
            if let device = DeviceManager.getCurrentDevice() {
                await ServiceManager.refreshAllServiesAndWidgets(device: device) // this can be pushed via hidden notification, also refresh/reload widgets
            }
            await DeviceManager.sync() // sync if out of sync. this will duplicate device update API call

            completion(true)
        }
    }

    @MainActor
    static func isBackgroundRefreshEnabled() -> Bool {
        if Thread.isMainThread {
            return UIApplication.shared.backgroundRefreshStatus == .available
        } else {
            var result = false
            DispatchQueue.main.sync {
                result = UIApplication.shared.backgroundRefreshStatus == .available
            }
            return result
        }
    }
}
