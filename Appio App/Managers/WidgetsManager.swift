//
//  WidgetsManager.swift
//  Appio
//
//  Created by gondo on 27/03/2025.
//

import WidgetKit

enum WidgetsManager {
    static func showTutorialBanner(service: ServiceEntity) -> Bool {
        guard let serviceLastWidgetRefresh = StorageManager.lastWidgetRefresh[service.id] as Date? else {
            return true
        }
        // Check if widget for given service was actie in the last 1 day
        return Date().timeIntervalSince(serviceLastWidgetRefresh) > 86400
    }

    static func fetchWidget(sericeId: String, widgetId: String) async throws -> WidgetEntity {
        let response = try await APIService.shared.fetchWidget(serviceId: sericeId, widgetId: widgetId)

        let widget: WidgetEntity
        if let existing = listBy(serviceId: sericeId).first(where: { $0.id == response.id }) {
            widget = WidgetEntity(from: response, existing: existing)
        } else {
            widget = WidgetEntity(from: response)
        }
        
        // NOTE: to use tmpConfig, uncomment this here
        // let widget = WidgetEntity.mock

        Log.shared.warning("Caching widget images")
        await cacheImages(from: widget.config)
        StorageManager.appendWidget(widget: widget, to: sericeId, overwrites: true)
        return widget
    }

    static func mergeNewWidgets(new: [WidgetResponse], serviceId: String) {
        let current = WidgetsManager.listBy(serviceId: serviceId)
        var merged: [WidgetEntity] = []

        for widgetResponse in new {
            if let existing = current.first(where: { $0.id == widgetResponse.id }) {
                merged.append(WidgetEntity(from: widgetResponse, existing: existing))
            } else {
                merged.append(WidgetEntity(from: widgetResponse))
            }
            Task {
                await cacheImages(from: widgetResponse.config)
            }
        }

        StorageManager.widgets[serviceId] = merged

        // Refresh all widgets with newly downloaded data
        Log.shared.warning("Refreshing widgets")
        WidgetCenter.shared.reloadAllTimelines() // reload all instead of single: WidgetCenter.shared.reloadTimelines(ofKind: WidgetShared.intentKind)
    }

    private static func cacheImages(from config: String) async {
        guard let links = parseLinks(from: config) else { return }

        for link in links {
            Log.shared.warning("Caching link: \(link)")
            await ImageManager.shared.preCacheImage(link)
        }
    }

    private static func parseLinks(from config: String) -> [String]? {
        let pattern = "\"src\"\\s*:\\s*\"([^\"]+)\""

        if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
            let nsString = config as NSString
            let matches = regex.matches(in: config, options: [], range: NSRange(location: 0, length: nsString.length))
            let imageURLs = matches.map { nsString.substring(with: $0.range(at: 1)) }

            return imageURLs
        }

        return nil
    }

    static func listBy(serviceId: String) -> [WidgetEntity] {
        return StorageManager.widgets[serviceId] ?? []
    }

    static func get(serviceId: String, id: String) -> WidgetEntity? {
        return StorageManager.widgets[serviceId]?.first(where: { $0.id == id })
    }
}
