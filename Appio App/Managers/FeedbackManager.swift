//
//  FeedbackManager.swift
//  Appio
//
//  Created by gondo on 11/09/2025.
//


import Foundation

enum FeedbackManager {
    static func submitFeedback(serviceId: String, deviceId: String, message: String) async -> Bool {
        do {
            return try await APIService.shared.submitFeedback(serviceId: serviceId, deviceId: deviceId, message: message)
        } catch {
            return false
        }
    }
}
