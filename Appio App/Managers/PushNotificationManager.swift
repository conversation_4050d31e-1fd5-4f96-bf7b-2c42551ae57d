//
//  PushNotificationManager.swift
//  Appio
//
//  Created by gondo on 05/03/2025.
//

import SwiftUI
import UserNotifications

/// <PERSON>les push notification registration and processing
enum PushNotificationManager {
    static func authorizationStatus() async -> UNAuthorizationStatus {
        await withCheckedContinuation { continuation in
            UNUserNotificationCenter.current().getNotificationSettings { settings in
                continuation.resume(returning: settings.authorizationStatus)
            }
        }
    }

    static func isEnabled() async -> <PERSON><PERSON> {
        await withCheckedContinuation { continuation in
            UNUserNotificationCenter.current().getNotificationSettings { settings in
                continuation.resume(returning: settings.authorizationStatus == .authorized || settings.authorizationStatus == .provisional)
            }
        }
    }

//    static func isDenied() async -> <PERSON>ol {
//        await withCheckedContinuation { continuation in
//            UNUserNotificationCenter.current().getNotificationSettings { settings in
//                continuation.resume(returning: settings.authorizationStatus == .denied)
//            }
//        }
//    }
//
//    static func isNotDetermined() async -> <PERSON><PERSON> {
//        await withCheckedContinuation { continuation in
//            UNUserNotificationCenter.current().getNotificationSettings { settings in
//                continuation.resume(returning: settings.authorizationStatus == .notDetermined)
//            }
//        }
//    }

    static func requestPermission(completion: ((Bool) -> Void)? = nil) {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if let error = error {
                print("Error requesting notification permission: \(error.localizedDescription)")
                completion?(false)
            } else {
                print("Notification permission granted: \(granted)")
                completion?(granted)
                if granted {
                    registerForRemoteNotifications()
                }
            }
        }
    }

    static func registerForRemoteNotifications() {
        DispatchQueue.main.async { // NOTE: replace with: Task { @MainActor in ... }
            UIApplication.shared.registerForRemoteNotifications() // will trigger didRegisterForRemoteNotifications
        }
    }

    /// Handles successful APNs device token registration
    static func didRegisterForRemoteNotifications(deviceToken: Data) {
        let token = deviceToken.map { String(format: "%02x", $0) }.joined()

        print("APN Token: \(token)")

        // Only update if token is not empty and has changed
        guard var device = DeviceManager.getCurrentDevice(),
              !token.isEmpty, // only if token is not empty
              device.apnToken != token else { return }

        print("APN token is new")

        Task {
            device.apnToken = token
            device.notifiationsEnabled = true
            StorageManager.device = device // this triggeres API update via DeviceSyncManager
        }
    }

    /// Handles failed APNs registration
    static func didFailToRegisterForRemoteNotifications(error: Error) {
        print("Failed to register for remote notifications: \(error.localizedDescription)")
    }

    /// custom buttons on push notifiation
    /// options: .foreground is not visible on Watch
    static func registerNotificationActions() {
        let yesAction = UNNotificationAction(identifier: "ACTION_YES",
                                             title: "Yes")

        let noAction = UNNotificationAction(identifier: "ACTION_NO",
                                            title: "No",
                                            options: [.destructive])

        // category needs to match: notification payload
        let yesNoCategory = UNNotificationCategory(identifier: "APPIO_YES_NO",
                                                   actions: [yesAction, noAction],
                                                   intentIdentifiers: [], // for Siri intents
                                                   options: [.customDismissAction, .hiddenPreviewsShowTitle, .hiddenPreviewsShowSubtitle])

        let defaultCategory = UNNotificationCategory(identifier: "APPIO_DEFAULT",
                                                     actions: [],
                                                     intentIdentifiers: [], // for Siri intents
                                                     options: [.customDismissAction, .hiddenPreviewsShowTitle, .hiddenPreviewsShowSubtitle])

        // Note: we can register multiple categories each with different actions
        UNUserNotificationCenter.current().setNotificationCategories([defaultCategory, yesNoCategory])
    }

    static func refreshNotificationToken() async {
        // We must have device to update apn token for
        guard let device = DeviceManager.getCurrentDevice() else { return }

        let isEnabled = await isEnabled()
        if device.apnToken.isEmpty || !isEnabled {
            await MainActor.run {
                registerForRemoteNotifications()
            }
        }
    }
}
