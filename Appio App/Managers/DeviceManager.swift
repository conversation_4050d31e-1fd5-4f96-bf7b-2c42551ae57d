//
//  DeviceManager.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation
import UIKit

enum DeviceManager {
    static func getCurrentDevice() -> DeviceEntity? {
        return StorageManager.device
    }

    static func matchFingerprint() async throws -> FingerprintResponse? {
        let deviceInfo = await DeviceInfoProvider.getDeviceInfo()
        let fingerprintRequest = FingerprintRequest(
            userAgent: deviceInfo.userAgent,
            screenResolution: deviceInfo.screenResolution,
            language: deviceInfo.language,
            timeOffset: deviceInfo.timeOffset
        )
        return try await APIService.shared.fingerprintMatch(fingerprintRequest: fingerprintRequest)
    }

    static func registerDevice(serviceId: String, customerUserId: String) async throws -> DeviceEntity {
        let response = try await APIService.shared.registerDevice(serviceId: serviceId, customerUserId: customerUserId)
        let device = DeviceEntity(from: response)
        StorageManager.device = device // overwrites existing
        return device
    }

    static func linkDeviceService(deviceId: String, serviceId: String, customerUserId: String) async throws -> Bool {
        return try await APIService.shared.linkService(deviceId: deviceId, serviceId: serviceId, customerUserId: customerUserId)
    }

    static func sync() async {
        guard var device = getCurrentDevice() else { return }

        // refresh in case device has updated
        device.deviceIdentifier = await UIDevice.current.deviceIdentifier
        device.osVersion = await UIDevice.current.systemVersion

        if device.isOutOfSync() {
            StorageManager.device = device // this triggeres API update via DeviceSyncManager
        }
    }
}
