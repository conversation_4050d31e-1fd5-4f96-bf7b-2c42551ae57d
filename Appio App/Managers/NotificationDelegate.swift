//
//  NotificationDelegate.swift
//  Appio
//
//  Created by gondo on 13/03/2025.
//

import SwiftUI
import UserNotifications

class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    static let shared = NotificationDelegate()

    override private init() {
        super.init() // singleton
    }

    private let maxHandledNotificationsCount = 1000
    private var handledNotifications: [String] = []
    private weak var appViewModel: AppViewModel?

    func setAppViewModel(_ viewModel: AppViewModel) {
        appViewModel = viewModel
    }

    /// Checks if notification was handled and maintains buffer size
    private func isHandled(_ requestId: String) -> Bool {
        if handledNotifications.contains(requestId) {
            return true
        }

        // Add new item at the end
        handledNotifications.append(requestId)

        // If we exceed the limit, remove the oldest item
        if handledNotifications.count > maxHandledNotificationsCount {
            handledNotifications.removeFirst()
        }
        return false
    }

    /// Handles notifications received when the app is in the foreground
    func userNotificationCenter(_: UNUserNotificationCenter,
                                willPresent notification: UNNotification,
                                withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void)
    {
        let requestId = notification.request.identifier
        let content = notification.request.content

        if isHandled(requestId) {
            print("⚠️ Duplicate Foreground Notification Ignored [ID: \(requestId)] - \(content.title) - \(content.body)")
            return
        }

        print("📩 Foreground Notification [ID: \(requestId)] - \(content.title) - \(content.body)")

        NotificationsStorer.storeNotification(content: content)
        notifyMainApp() // refresh notifications UI

        // Display banner and play sound
        completionHandler([.banner, .sound, .list])
    }

    /// Handles notification tap (when user opens a notification) also works for cold start/background
    func userNotificationCenter(_: UNUserNotificationCenter,
                                didReceive response: UNNotificationResponse,
                                withCompletionHandler completionHandler: @escaping () -> Void)
    {
        let content = response.notification.request.content
        print("User tapped notification: \(content.title) - \(content.body)")
        Log.shared.warning("User tapped notification: \(content.title) - \(content.body)")

        guard let data = content.userInfo["data"] as? [String: Any],
              let serviceId = data["service_id"] as? String,
              let notificationId = data["notification_id"] as? String
        else {
            completionHandler()
            return
        }

        // if category setup with option .customDismissAction, detect dismiss: if response.actionIdentifier == UNNotificationDismissActionIdentifier

        NotificationsStorer.storeNotification(content: content)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { // has to have delay to wait for main UI
            Log.shared.warning("navigating with delay. service: \(serviceId), notification: \(notificationId)")
            self.appViewModel?.navigateToNotification(serviceId: serviceId, notificationId: notificationId, action: response.actionIdentifier)
        }

        completionHandler()
    }

    // needed to refresh the UI when the app is in foreground
    private func notifyMainApp() {
        // can't pass any data. just signal
        let notificationName = CFNotificationName("so.appio.app.notification-delegate" as CFString)
        CFNotificationCenterPostNotification(CFNotificationCenterGetDarwinNotifyCenter(),
                                             notificationName,
                                             nil,
                                             nil,
                                             true)
    }
}
