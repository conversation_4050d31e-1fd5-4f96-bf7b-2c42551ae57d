//
//  NotificationManager.swift
//  Appio
//
//  Created by gondo on 19/03/2025.
//

import SwiftUI

enum NotificationManager {

    static func refreshAllNotificationsBy(serviceId: String) async -> [NotificationEntity] {
        print("Refreshing notifications for: \(serviceId)")

        do {
            guard let device = DeviceManager.getCurrentDevice() else {
                return []
            }
            let cursor = getLatest(serviceId: serviceId)?.id ?? ""
            let list = try await APIService.shared.fetchAllNotifications(serviceId: serviceId, deviceId: device.id, cursor: cursor)
            let newNotifications = list.map { NotificationEntity(from: $0) }

            return mergeNewNotifications(serviceId: serviceId, new: newNotifications, current: listBy(serviceId: serviceId))
        } catch ApiError.debounce {
            // Silently ignore debounce errors
            return listBy(serviceId: serviceId)
        } catch {
            Log.shared.error("Refreshing notifications for: \(serviceId) failed with error: \(error)")
            return []
        }
    }

    // add new Notifications and keep existing unchanged to keep local state (receiedAt, openedAt)
    static func mergeNewNotifications(serviceId: String, new: [NotificationEntity], current: [NotificationEntity]) -> [NotificationEntity] {
        let currentIDs = Set(current.map { $0.id })

        let newUnique = new.filter { !currentIDs.contains($0.id) }
        let merged = current + newUnique
        let sorted = merged.sorted { $0.receivedAt > $1.receivedAt }

        StorageManager.notifications[serviceId] = sorted
        return sorted
    }

    static func listBy(serviceId: String) -> [NotificationEntity] {
        StorageManager.notifications[serviceId] ?? []
    }

    static func get(serviceId: String, id: String) -> NotificationEntity? {
        StorageManager.notifications[serviceId]?.first(where: { $0.id == id })
    }

    static func getLatest(serviceId: String) -> NotificationEntity? {
        listBy(serviceId: serviceId).max(by: { $0.id < $1.id })
    }
}
