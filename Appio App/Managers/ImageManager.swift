//
//  ImageManager.swift
//  Appio
//
//  Created by gondo on 05/03/2025.
//

import CryptoKit
import Foundation
import SwiftUI

actor ImageManager {
    static let shared = ImageManager()
    private let fileManager = FileManager.default
    private let cacheDirectory: URL

    private init() {
        if let containerURL = fileManager.containerURL(forSecurityApplicationGroupIdentifier: "group.so.appio.app") {
            cacheDirectory = containerURL.appendingPathComponent("ImageCache", isDirectory: true)
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        } else {
            fatalError("Unable to access App Group container")
//            let paths = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)
//            cacheDirectory = paths[0].appendingPathComponent("ImageCache")
//            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
    }

    func getImage(for url: URL) async throws -> UIImage {
        if let image = loadImageFromCache(url) {
            print("Image loaded from cache: \(url)")
            return image
        }

        return try await downloadAndCacheImage(from: url)
    }

    func preCacheImage(_ url: String?, force: Bool = false) async {
        guard let url = url, let imageURL = URL(string: url) else { return }

        if !force, isCached(imageURL) {
            return
        }

        do {
            _ = try await getImage(for: imageURL)
        } catch {
            Log.shared.error("Failed to cache \(imageURL) with error: \(error)")
        }
    }

    func clearCache() {
        try? fileManager.removeItem(at: cacheDirectory)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }

    private static func getCacheFileName(for url: URL) -> String {
        let data = Data(url.absoluteString.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined() + ".cache"
    }

    private func isCached(_ url: URL) -> Bool {
        let fileName = Self.getCacheFileName(for: url)
        let fileURL = cacheDirectory.appendingPathComponent(fileName)

        return fileManager.fileExists(atPath: fileURL.path)
    }

    private func loadImageFromCache(_ url: URL) -> UIImage? {
        let fileName = Self.getCacheFileName(for: url)
        let fileURL = cacheDirectory.appendingPathComponent(fileName)

        guard fileManager.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data)
        else {
            return nil
        }
        return image
    }

    private func downloadAndCacheImage(from url: URL) async throws -> UIImage {
        let (data, _) = try await URLSession.shared.data(from: url)
        guard let image = UIImage(data: data) else {
            throw ImageError.invalidImageData
        }

        // Save to cache
        let fileName = Self.getCacheFileName(for: url)
        let fileURL = cacheDirectory.appendingPathComponent(fileName)
        try data.write(to: fileURL)

        return image
    }
}

extension ImageManager {
    // NOTE: lots of replicated code to make this non async. Due to ImageManager being actor
    static func loadImageFromCacheSync(_ url: URL) -> UIImage? {
        let fileManager = FileManager.default

        guard let containerURL = fileManager.containerURL(forSecurityApplicationGroupIdentifier: "group.so.appio.app") else {
            return nil
        }

        let cacheDirectory = containerURL.appendingPathComponent("ImageCache", isDirectory: true)
        let fileName = Self.getCacheFileName(for: url)
        let fileURL = cacheDirectory.appendingPathComponent(fileName)

        guard fileManager.fileExists(atPath: fileURL.path),
              let data = try? Data(contentsOf: fileURL),
              let image = UIImage(data: data)
        else {
            return nil
        }
        return image
    }
}

enum ImageError: Error {
    case invalidImageData
}

// MARK: - SwiftUI Image Extension

extension Image {
    static func cached(url: URL) -> Image {
        if let uiImage = ImageManager.loadImageFromCacheSync(url) {
            return Image(uiImage: uiImage)
        }
        return placeholder()
    }

    static func placeholder() -> Image {
        Image(systemName: "photo")
    }
}
