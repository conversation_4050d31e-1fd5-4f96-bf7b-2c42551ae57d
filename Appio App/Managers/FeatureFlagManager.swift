//
//  FeatureFlagManager.swift
//  Appio
//
//  Created by gondo on 23/04/2025.
//

import Foundation

enum FeatureFlagManager {
    static func update() async {
        do {
            guard let response = try await APIService.shared.fetchFeatureFlags() else { return }
            StorageManager.featureFlags = response
        } catch {
            Log.shared.error("Failed to load feature flags for version: \(AppVersion.current())")
        }
    }

    static func get<T>(key: String) -> T? {
        guard let featureFlags = StorageManager.featureFlags,
              let configData = featureFlags.config.data(using: .utf8)
        else {
            return nil
        }
        do {
            let config = try JSONDecoder().decode(AnyDecodable.self, from: configData)
            if let dict = config.value as? [String: Any],
               let value = dict[key] as? T
            {
                return value
            }
            Log.shared.warning("Feature Flag config key: \(key) was not found. id: \(featureFlags.id), version: \(featureFlags.version), config: \(featureFlags.config)")
            return nil
        } catch {
            Log.shared.warning("Faild decoding Feature Flag config into Json. id: \(featureFlags.id), version: \(featureFlags.version), config: \(featureFlags.config)")
            return nil
        }
    }
}
