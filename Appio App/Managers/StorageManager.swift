//
//  StorageManager.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Combine
import Foundation

protocol Storable: Codable {
    var id: String { get }
}

enum StorageValidationError: LocalizedError {
    case invalidData
    case corruptedData
    case invalidStructure
    case decodingError(Error)

    var errorDescription: String? {
        switch self {
        case .invalidData: return "Invalid data format"
        case .corruptedData: return "Data appears to be corrupted"
        case .invalidStructure: return "Invalid data structure"
        case let .decodingError(error): return "Decoding error: \(error.localizedDescription)"
        }
    }
}

enum StorageManager {
    static let devicePublisher = CurrentValueSubject<DeviceEntity?, Never>(device)
    static let servicesPublisher = CurrentValueSubject<[ServiceEntity], Never>(services)
    static let notificationsPublisher = CurrentValueSubject<[String: [NotificationEntity]], Never>(notifications)
    static let widgetsPublisher = CurrentValueSubject<[String: [WidgetEntity]], Never>(widgets)
    static let featureFlagsPublisher = CurrentValueSubject<FeatureFlagsEntity?, Never>(featureFlags)
    static let validationErrorPublisher = PassthroughSubject<StorageValidationError, Never>()

    private static let deviceKey = "device"
    private static let servicesKey = "services"
    private static let notificationsKey = "notifications"
    private static let widgetsKey = "widgets"
    private static let featureFlagsKey = "featureflags"
    private static let lastWidgetRefreshKey = "lastWidgetRefresh"

    static var userDefaults: UserDefaults {
        UserDefaults(suiteName: "group.so.appio.app") ?? UserDefaults.standard
    }

    private static func store<T: Codable>(_ value: T?, forKey key: String) {
        do {
            if let value = value {
                let data = try JSONEncoder().encode(value)
                if !validateDataStructure(data) {
                    validationErrorPublisher.send(.invalidStructure)
                    return
                }
                userDefaults.set(data, forKey: key)
            } else {
                userDefaults.removeObject(forKey: key)
            }
        } catch {
            validationErrorPublisher.send(.decodingError(error))
        }
    }

    private static func retrieve<T: Codable>(forKey key: String) -> T? {
        guard let data = userDefaults.data(forKey: key) else { return nil }
        if !validateDataIntegrity(data) {
            validationErrorPublisher.send(.corruptedData)
            return nil
        }
        do {
            return try JSONDecoder().decode(T.self, from: data)
        } catch {
            validationErrorPublisher.send(.decodingError(error))
            return nil
        }
    }

    private static func retrieveArray<T: Codable>(forKey key: String) -> [T] {
        return retrieve(forKey: key) ?? []
    }

    private static func validateDataStructure(_ data: Data) -> Bool {
        return (try? JSONSerialization.jsonObject(with: data)) != nil
    }

    private static func validateDataIntegrity(_ data: Data) -> Bool {
        return !data.isEmpty && data.count < 10_000_000 // 10MB limit
    }

    static var device: DeviceEntity? {
        get { retrieve(forKey: deviceKey) }
        set {
            store(newValue, forKey: deviceKey)
            devicePublisher.send(newValue)
        }
    }

    static var featureFlags: FeatureFlagsEntity? {
        get { retrieve(forKey: featureFlagsKey) }
        set {
            store(newValue, forKey: featureFlagsKey)
            featureFlagsPublisher.send(newValue)
        }
    }

    static var services: [ServiceEntity] {
        get { retrieveArray(forKey: servicesKey).sorted { $0.id > $1.id } }
        set {
            let sortedDESC = newValue.sorted { $0.id > $1.id }
            store(sortedDESC, forKey: servicesKey)
            servicesPublisher.send(sortedDESC)
        }
    }

    static func appendService(service: ServiceEntity, overwrites: Bool = false) {
        var list = StorageManager.services

        if let existingIndex = list.firstIndex(where: { $0.id == service.id }) {
            if overwrites {
                list[existingIndex] = service
            }
            // otherwise ignore
        } else {
            list.insert(service, at: 0) // add to the beginig since we are displaying DESC
        }

        /// to trigger setter
        services = list
    }

    static var notifications: [String: [NotificationEntity]] {
        get {
            let dict = retrieve(forKey: notificationsKey) as [String: [NotificationEntity]]? ?? [:]
            return dict.mapValues { $0.sorted { $0.receivedAt > $1.receivedAt } }
        }
        set {
            let sortedDict = newValue.mapValues { $0.sorted { $0.receivedAt > $1.receivedAt } }
            store(sortedDict, forKey: notificationsKey)

            print("Notifications stored: \(sortedDict)")

            notificationsPublisher.send(sortedDict)
        }
    }

    static func appendNotification(notification: NotificationEntity, to serviceId: String, overwrites: Bool = false) {
        var grouped = StorageManager.notifications
        var list = grouped[serviceId] ?? []

        if let existingIndex = list.firstIndex(where: { $0.id == notification.id }) {
            if overwrites {
                list[existingIndex] = notification
            }
            // otherwise ignore
        } else {
            list.insert(notification, at: 0) // add to the beginig since we are displaying DESC
        }

        grouped[serviceId] = list
        /// to trigger setter
        notifications = grouped
    }

    static var widgets: [String: [WidgetEntity]] {
        get {
            retrieve(forKey: widgetsKey) as [String: [WidgetEntity]]? ?? [:]
        }
        set {
            store(newValue, forKey: widgetsKey)
            widgetsPublisher.send(newValue)
        }
    }

    static func appendWidget(widget: WidgetEntity, to serviceId: String, overwrites: Bool = false) {
        var grouped = StorageManager.widgets
        var list = grouped[serviceId] ?? []

        if let existingIndex = list.firstIndex(where: { $0.id == widget.id }) {
            if overwrites {
                list[existingIndex] = widget
            }
            // otherwise ignore
        } else {
            list.insert(widget, at: 0) // add to the beginig since we are displaying DESC
        }

        grouped[serviceId] = list
        /// to trigger setter
        widgets = grouped
    }

    // Store last widget refresh for each service
    static var lastWidgetRefresh: [String: Date] {
        get {
            retrieve(forKey: lastWidgetRefreshKey) as [String: Date]? ?? [:]
        }
        set {
            store(newValue, forKey: lastWidgetRefreshKey)
        }
    }
}
