//
//  IntroScreen.swift
//  Appio App
//
//  Created by gondo on 24/10/2024.
//

import SwiftUI

struct IntroScreen: View {
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    @State private var showInfo = false

    private let maxContentWidth: CGFloat = 400
    private let maxContentHeight: CGFloat = 800

    var body: some View {
        NavigationStack {
            VStack(spacing: 24) {
                Spacer()

                // MARK: - Headline

                VStack(alignment: .center) {
                    PlugShape(color: Color.primary.opacity(0.7))
                        .frame(maxWidth: 120)
                        .padding(.bottom, 16)

                    Text("Let's get you")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(.primary)

                    Text("Connected")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundStyle(Color.accentColor)
                }

                // MARK: - Instructions

//                Text("Join the service.")
//                    .font(.body)
//                    .foregroundStyle(.primary)
//                    .padding(.horizontal, 40)
//                    .multilineTextAlignment(.center)

                Spacer()

                // MARK: - Buttons

                VStack {
                    if let variant: String = FeatureFlagManager.get(key: "intro"), variant == "A" {
                        Text("To get started, connect to\na service that uses Appio.\nAppio is a companion app\nfor https://appio.so")
                            .multilineTextAlignment(.center)
                            .frame(height: 90)
                            .padding(.bottom, 20)
                        
                        AppioButton("Try Demo Service") {
                            if let url = URL(string: "https://demo.appio.so/?platform=ios&app=true") {
                                UIApplication.shared.open(url, options: [:], completionHandler: nil)
                            }
                        }
                        .padding(UIConstants.spacing)
                    } else {
                        AppioButton("Continue setup") {
                            if let url = URL(string: "https://app.appio.so/?platform=ios&app=true") {
                                UIApplication.shared.open(url, options: [:], completionHandler: nil)
                            }
                        }
                        .padding(UIConstants.spacing)
                        
                        Text("If you came from a browser")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                            .padding(1)
                    }

                    HStack {
                        VStack { Divider().background(Color.secondary.opacity(0.5)) }.padding(.leading, 40)
                        Text("or")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                            .padding(10)
                        VStack { Divider().background(Color.secondary.opacity(0.5)) }.padding(.trailing, 40)
                    }
                    .padding(.vertical, 16)

                    NavigationLink {
                        QRCodeScreen()
                    } label: {
                        Text("Scan the QR code")
                            .padding(.bottom, 16)
                    }
                    .padding(.bottom, 16)
                }
            }
            .frame(maxWidth: maxContentWidth, maxHeight: maxContentHeight)
            .padding()
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showInfo = true
                    } label: {
                        Image(systemName: "info.circle")
                    }
                }
            }
            .accentColor(Color(UIColor.systemBlue)) // fix wrong blue when in NavigationStack
            .sheet(isPresented: $showInfo) {
                AppioFooterView(service: nil, device: nil)
                    .padding(.top, 32)
                    .presentationDetents(
                        horizontalSizeClass == .compact ? [.height(200)] : [.medium]
                    )
                    .presentationDragIndicator(.visible)
            }
        }
    }
}

#Preview {
    IntroScreen()
}
