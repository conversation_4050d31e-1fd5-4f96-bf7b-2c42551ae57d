//
//  QRCodeScreen.swift
//  Appio App
//
//  Created by gondo on 24/10/2024.
//

import SwiftUI

struct QRCodeScreen: View {
    @Environment(\.scenePhase) private var scenePhase
    @EnvironmentObject private var appViewModel: AppViewModel

    @StateObject private var viewModel = QRScannerModel()
    @State private var shouldHideBackButton: Bool = true

    var body: some View {
        ZStack {
            contentView()
        }
        .task {
            await checkCameraPermission()
        }
        .onChange(of: scenePhase) {
            if scenePhase == .active {
                Task {
                    await checkCameraPermission()
                }
            }
        }
        .onChange(of: viewModel.appioURL) {
            if let url = viewModel.appioURL {
                Task {
                    print("Scanned valid Appio URL and opening it")
                    await appViewModel.start(with: url)
                }
            }
        }
        .navigationBarBackButtonHidden(shouldHideBackButton)
    }

    @ViewBuilder
    private func contentView() -> some View {
        // BackgroundColor()

        switch viewModel.cameraStatus {
        case .allowed:
            cameraView()
        case .denied:
            CameraDisabled()
        case .unknonw:
            // Default iOS permission request without any visual
            EmptyView()
        }
    }

    @ViewBuilder
    private func cameraView() -> some View {
        #if targetEnvironment(simulator)
            Color.gray
                .ignoresSafeArea()
        #endif

        QRScannerCamera(viewModel: viewModel)

        #if targetEnvironment(simulator)
            Button {
                viewModel.scannedValue = "appio://appio/?service=demo_svc_01jndpe4fnn90wkpvw4d28hwd0&user=user1"
            } label: {
                Text("Valid Code")
                    .padding()
                    .background(Color.green)
                    .foregroundStyle(.white)
            }
        #endif
    }

    private func checkCameraPermission() async {
        print("checking camera permissions")
        await viewModel.checkCameraPermission()
        shouldHideBackButton = (viewModel.cameraStatus == .allowed)
    }
}

#Preview {
    QRCodeScreen()
}
