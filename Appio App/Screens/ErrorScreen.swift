//
//  ErrorScreen.swift
//  Appio
//
//  Created by gondo on 06/03/2025.
//

import SwiftUI

struct ErrorScreen: View {
    @EnvironmentObject var appViewModel: AppViewModel

    let error: String

    // Add retry state
    @State private var isRetrying = false

    var body: some View {
        VStack(spacing: 24) {
            Text("An error occurred")
                .multilineTextAlignment(.center)
                .font(.largeTitle)
                .fontWeight(.bold)
                .foregroundStyle(.primary)
                .accessibilityAddTraits(.isHeader)

            Text(error)
                .font(.body)
                .foregroundStyle(.primary)
                .padding(.horizontal, 40)
                .multilineTextAlignment(.center)
                .accessibilityLabel("Error message: \(error)")

            Button {
                retry()
            } label: {
                if isRetrying {
                    ProgressView()
                        .controlSize(.regular)
                } else {
                    Text("Retry")
                        .font(.title3)
                }
            }
            .disabled(isRetrying)
            .padding(.top, 8)
        }
        .padding()
    }

    private func retry() {
        guard !isRetrying else { return }

        isRetrying = true
        Task {
            await appViewModel.start()

            // Add delay to prevent rapid retries
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 seconds

            isRetrying = false
        }
    }
}

#Preview {
    ErrorScreen(error: "Custom error message.")
        .environmentObject(AppViewModel())
}
