//
//  LaunchFadeLogoScreen.swift
//  Appio
//
//  Created by gondo on 11/03/2025.
//

import SwiftUI

struct LaunchFadeLogoScreen: View {
    @State private var logoOpacity: Double = 0.2 // matching LaunchScreen.storyboard
    @State private var logoSize: CGFloat = 50 // matching LaunchScreen.storyboard

    @State private var isAnimatingBg = false
    @State private var isAnimatingLogo = false

    private let initAnimationDelay: TimeInterval = 0.3 // small delay to give <PERSON> time to mount this view
    private let logoAnimationDuration: TimeInterval = 0.5
    private let bgAnimationDuration: TimeInterval = 0.3

    var body: some View {
        ZStack {
            BackgroundColor() // matching LaunchScreen.storyboard color and filling the space and covering the content while animation logo
                .opacity(isAnimatingBg ? 0 : 1)
                .animation(.easeIn(duration: bgAnimationDuration).delay(initAnimationDelay + logoAnimationDuration), value: isAnimatingBg)

            Image("LaunchImage")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: isAnimatingLogo ? logoSize * 2 : logoSize)
                .opacity(isAnimatingLogo ? 0 : logoOpacity)
                .animation(.easeIn(duration: logoAnimationDuration).delay(initAnimationDelay), value: isAnimatingLogo)
        }
        .ignoresSafeArea()
        .onAppear {
            isAnimatingBg = true
            isAnimatingLogo = true
        }
    }
}

#Preview {
    LaunchFadeLogoScreen()
}
