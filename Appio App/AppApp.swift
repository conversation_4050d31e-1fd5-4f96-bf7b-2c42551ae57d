//
//  AppApp.swift
//  Appio App
//
//  Created by gondo on 22/10/2024.
//

import SwiftUI
import WidgetKit

@main
struct Appio_AppApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var appViewModel: AppViewModel = .init()
    @Environment(\.scenePhase) private var scenePhase

    var body: some Scene {
        WindowGroup {
            MainView()
                .environmentObject(appViewModel)
                .task {
                    await appViewModel.initStart()
                }
                .onChange(of: scenePhase) {
                    print("screen phase changed: \(scenePhase)")
                    switch scenePhase {
                    case .active:
                        BackgroundFetchManager.refreshData { _ in }
                    case .background:
                        BackgroundFetchManager.scheduleAppRefresh() // backup suggested by AI
                    default:
                        break
                    }
                }
                // only for custom URL schemes, not for Universal Links
                .onOpenURL { url in
                    Task {
                        await DeepLinkCoordinator.shared.handle(url: url, appViewModel: appViewModel)
                    }                }
                // handling Universal Links, this is equivalent of AppDelegate's https://developer.apple.com/documentation/xcode/supporting-universal-links-in-your-app#Update-your-app-delegate-to-respond-to-a-universal-link
                .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
                    if let url = userActivity.webpageURL {
                        Task {
                            await DeepLinkCoordinator.shared.handle(url: url, appViewModel: appViewModel)
                        }
                    }
                }
        }
    }
}
