//
//  AppError.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import SwiftUI

enum AppError: LocalizedError {
    case networkError(Error)
    case flowError(Error)
    case deviceError(Error)

    var errorDescription: String? {
        switch self {
        case let .networkError(error):
            return "Network error: \(error.localizedDescription)"
        case let .flowError(error):
            return "Flow error: \(error.localizedDescription)"
        case let .deviceError(error):
            return "Device error: \(error.localizedDescription)"
        }
    }
}
