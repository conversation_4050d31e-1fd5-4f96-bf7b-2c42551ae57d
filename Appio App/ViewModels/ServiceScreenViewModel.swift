//
//  ServiceScreenViewModel.swift
//  Appio
//
//  Created by gondo on 21/03/2025.
//

import Combine
import Foundation
import SwiftUI

@MainActor
final class ServiceScreenViewModel: ObservableObject {
    @Published private(set) var service: ServiceEntity
    @Published private(set) var notifications: [NotificationEntity] = []

    private var cancellables = Set<AnyCancellable>()

    init(service: ServiceEntity) {
        self.service = service
        setupSubscriptions()
        refreshNotifications() // Initial fetch
    }
    
    var device: DeviceEntity? {
        DeviceManager.getCurrentDevice()
    }

    private func setupSubscriptions() {
        StorageManager.servicesPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] services in
                guard let self = self,
                      let updatedService = services.first(where: { $0.id == self.service.id }) else { return }
                self.service = updatedService
            }
            .store(in: &cancellables)

        StorageManager.notificationsPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] notifications in
                guard let self = self,
                      let updatedNotification = notifications[self.service.id] else { return }
                self.notifications = updatedNotification
            }
            .store(in: &cancellables)
    }

    func markPreviewAsViewed() {
        var updatedService = service
        updatedService.markPreviewViewed()
        ServiceManager.updateService(service: updatedService)
        service = updatedService
    }

    func refreshNotifications() {
        // RELEASE: preview data
        // notifications = [NotificationEntity.mock, NotificationEntity.mockC, NotificationEntity.mockD]
        Task { @MainActor in
            self.notifications = await NotificationManager.refreshAllNotificationsBy(serviceId: service.id)
        }
    }

    func reloadService() {
        // Update to get latest version from ServiceManager
        if let updatedService = ServiceManager.get(id: service.id) {
            // print("- Service \(service) \n- Reloaded \(updatedService)\n")
            service = updatedService
        }
    }

    deinit {
        cancellables.removeAll()
    }
}
