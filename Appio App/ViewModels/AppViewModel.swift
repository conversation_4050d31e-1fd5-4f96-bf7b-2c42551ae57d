//
//  AppViewModel.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Combine
import Foundation
import SwiftUI

@MainActor
final class AppViewModel: ObservableObject {
    private var launched: Bool = false // resolve race condition between .task and .onOpenURL

    @Published private(set) var state: AppViewState = .launch
    @Published private(set) var isLoading = false // to show loader in overlay
    @Published var navigationPath = NavigationPath()
    @Published private(set) var services: [ServiceEntity] = []

    var currentService: ServiceEntity? {
        if case let .service(service) = state {
            return service
        }
        return services.first
    }

    // MARK: - Storage observer, Data sync

    private var cancellables = Set<AnyCancellable>()
    private var syncManager: DeviceSyncManager?

    init() {
        setupSubscriptions()
        syncManager = DeviceSyncManager() // Start monitoring device changes
        services = ServiceManager.list()
    }

    private func setupSubscriptions() {
        StorageManager.validationErrorPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] error in
                Log.shared.error("Storage validation error: \(error.localizedDescription)")
                // N2H: Error should be overlay not custom screen
                self?.changeScreen(.error(error.localizedDescription))
            }
            .store(in: &cancellables)

        StorageManager.servicesPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] services in
                self?.services = services
            }
            .store(in: &cancellables)
    }

    // MARK: - public

    var device: DeviceEntity? {
        DeviceManager.getCurrentDevice()
    }

    // runs only once and only if app was not started via URL
    func initStart() async {
        // Wait briefly to allow URL processing if app was launched with URL
        try? await Task.sleep(for: .milliseconds(100))

        guard !launched else { return }
        launched = true

        Log.shared.warning("lanuched without url")

        await start()
    }

    func start() async {
        launched = true
        print("Starting flow")
        await startWithOptionalURL(nil)
    }

    func start(with url: URL) async {
        launched = true
        print("Starting flow with url: \(url)")
        await startWithOptionalURL(url)
    }

    func navigateToService(serviceId: String) {
        guard let service = ServiceManager.get(id: serviceId) else { return }

        changeScreen(.service(service)) // change service to force UI rebuilt

        navigationPath = NavigationPath() // reset
        if services.count > 1 { // we have extra AllServices view. otherwise we are already on ServiceScreen
            navigationPath.append(service)
        }
    }

    func navigateToNotification(serviceId: String, notificationId: String, action _: String) {
        // N2H: consider notification tapped `action`, ignored for now
        guard let service = ServiceManager.get(id: serviceId),
              let notification = NotificationManager.get(serviceId: serviceId, id: notificationId) else { return }

        print("Opening notification: \(notification.id) \n for service: \(service.id)")
        navigateToService(serviceId: service.id)
        navigationPath.append(notification)
    }

    // MARK: - private

    private func startWithOptionalURL(_ url: URL?) async {
        print("Loading start")
        isLoading = true

        await FeatureFlagManager.update() // no throw, or catch if no internet and show error? extra function. we cant exit without `isloading = false`

        if let url = url,
           let params = parseParams(from: url)
        {
            await startFlow(with: params)
        } else {
            await startFlow()
        }

        print("Loading end")
        isLoading = false
    }

    private func parseParams(from url: URL) -> URLparams? {
        do {
            return try URLparser.parseParams(from: url)
        } catch {
            return nil
        }
    }

    // Centralized async initializer to determine app flow.
    private func startFlow(with params: URLparams? = nil) async {
        if let params {
            print("Params detected: \(params)")

            let nextState = await processLaunchParamsState(params: params)
            changeScreen(nextState)
            return
        }

        if let _ = DeviceManager.getCurrentDevice(),
           services.count > 0
        {
            print("No params, but Device detected and services not null")
            changeScreen(showService(service: services[0]))

            return
        }

        do {
            if let service = try await checkFingerprintMatch() {
                changeScreen(.service(service))
            } else {
                changeScreen(.intro)
            }
        } catch ApiError.unexpectedStatusCode(202, _) {
            // TODO: show code input
            Log.shared.error("Fingerprint not sure.")
            changeScreen(.error("Internal Error"))
        } catch {
            Log.shared.error("Fingerprint check failed: \(error.localizedDescription)")
            changeScreen(.error(error.localizedDescription))
        }
    }

    private func changeScreen(_ newState: AppViewState) {
        print("SCREEN: \(newState)")
        state = newState
    }

    private func processLaunchParamsState(params: URLparams) async -> AppViewState {
        // TODO: these 2 functions can throw exception that is currently not handled. or these functions should not throw exceptions?
        async let device = try getOrRegisterDevice(params: params)
        async let service = try getService(serviceId: params.serviceId, customerUserId: params.customerUserId ?? "")

        do {
            let (deviceResult, serviceResult) = try await (device, service)

            if let _ = deviceResult {
                print("We have Device and Service.")
                return showService(service: serviceResult)
            }

            return .intro
        } catch {
            print("processLaunchParamsState with error: \(error)")
            return .error(error.localizedDescription)
        }
    }

    private func showService(service: ServiceEntity) -> AppViewState {
        navigateToService(serviceId: service.id)
        return .service(service)
    }

    /// Returns the existing device if available and linking with service, otherwise registers a new one.
    private func getOrRegisterDevice(params: URLparams) async throws -> DeviceEntity? {
        if let device = DeviceManager.getCurrentDevice() {
            if let customerUserId = params.customerUserId {
                // NOTE: might be overkill for existing service, but required for new service
                try await linkDeviceService(deviceId: device.id, serviceId: params.serviceId, customerUserId: customerUserId)
            }
            print("Device detected")
            return device
        }

        if let customerUserId = params.customerUserId {
            print("Registering Device with Service: \(params.serviceId) and Customer User Id: \(customerUserId)")
            return try await DeviceManager.registerDevice(
                serviceId: params.serviceId,
                customerUserId: customerUserId,
            )
        }

        return nil
    }

    private func linkDeviceService(deviceId: String, serviceId: String, customerUserId: String) async throws {
        print("Linking Device: \(deviceId) with Service: \(serviceId) and Customer User Id: \(customerUserId)")
        let ok = try await DeviceManager.linkDeviceService(deviceId: deviceId, serviceId: serviceId, customerUserId: customerUserId)
        if !ok {
            print("Linking Device with Service was unsuccessful.")
            throw AppError.deviceError(NSError(domain: "AppViewModel",
                                               code: 0,
                                               userInfo: [NSLocalizedDescriptionKey: "Linking returned false"]))
        }
    }

    private func getService(serviceId: String, customerUserId: String) async throws -> ServiceEntity {
        print("Fetching Service: \(serviceId)")
        return try await ServiceManager.fetchServiceWithWidgetsBy(id: serviceId, customerUserId: customerUserId)
    }

    private func checkFingerprintMatch() async throws -> ServiceEntity? {
        print("Checking Fingerprint Match")

        guard let fingerprintMatch = try await DeviceManager.matchFingerprint(),
              !fingerprintMatch.serviceId.isEmpty,
              !fingerprintMatch.customerUserId.isEmpty
        else {
            return nil
        }

        // Register device and fetch service in parallel
        async let deviceTask = DeviceManager.registerDevice(
            serviceId: fingerprintMatch.serviceId,
            customerUserId: fingerprintMatch.customerUserId,
        )
        async let serviceTask = getService(serviceId: fingerprintMatch.serviceId, customerUserId: fingerprintMatch.customerUserId)

        // Wait for both tasks to complete
        let (_, service) = try await (deviceTask, serviceTask)
        return service
    }

    deinit {
        for c in cancellables {
            c.cancel()
        }
    }
}
