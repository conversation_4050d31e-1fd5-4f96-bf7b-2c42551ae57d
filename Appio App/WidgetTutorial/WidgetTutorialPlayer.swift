//
//  WidgetTutorialPlayer.swift
//  Appio
//
//  Created by gondo on 13/04/2025.
//

import AVKit
import SwiftUI

struct WidgetTutorialPlayer: UIViewControllerRepresentable {
    @ObservedObject var playerController: WidgetTutorialPlayerController

    func updateUIViewController(_: AVPlayerViewController, context _: Context) {
        // Update the AVPlayerViewController when SwiftUI view updates
        // (e.g., handle updates when the underlying AVPlayer changes)
    }

    func makeUIViewController(context _: Context) -> AVPlayerViewController {
        return playerController.avPlayerViewController
    }
}
