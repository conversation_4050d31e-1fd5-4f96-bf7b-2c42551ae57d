//
//  WidgetTutorialPlayerController.swift
//  Appio
//
//  Created by gondo on 13/04/2025.
//

import AVKit
import SwiftUI

// NOTE: only works on real device not in Simulator
class WidgetTutorialPlayerController: NSObject, ObservableObject {
    private var player: AVPlayer?
    private(set) var avPlayerViewController: AVPlayerViewController = .init()

    // MARK: - Initialization and Setup

    func initPlayer(colorScheme: ColorScheme) {
        setupPlayer(colorScheme: colorScheme)
        setupAVPlayerViewController()
    }

    // MARK: - AVPlayer Setup

    private func setupPlayer(colorScheme: ColorScheme) {
        let tutorialFile = colorScheme == .dark ? "dark" : "light"
        guard let videoURL = Bundle.main.url(forResource: tutorialFile, withExtension: "mov") else {
            print("Invalid video link")
            return
        }

        // Initialize AVPlayer with the provided video link
        player = AVPlayer(url: videoURL)

        // Enable looping by setting action at end to none
        player?.actionAtItemEnd = .none
        
        // Mute
        player?.isMuted = true

        // Observe video end notification to restart playback
        NotificationCenter.default.addObserver(forName: .AVPlayerItemDidPlayToEndTime,
                                               object: player?.currentItem,
                                               queue: .main)
        { [weak self] _ in
            self?.player?.seek(to: .zero)
            self?.player?.play()
        }

        setupMetadata()
    }

    // Set up metadata for the current AVPlayerItem
    private func setupMetadata() {
        let title = AVMutableMetadataItem()
        title.identifier = .commonIdentifierTitle
        title.value = "Add Widget Tutorial" as NSString
        title.extendedLanguageTag = "und"

        let artist = AVMutableMetadataItem()
        artist.identifier = .commonIdentifierArtist
        artist.value = "Appio" as NSString
        artist.extendedLanguageTag = "und"

        let artwork = AVMutableMetadataItem()
        setupArtworkMetadata(artwork)

        // Set external metadata for the current AVPlayerItem
        player?.currentItem?.externalMetadata = [title, artwork]
    }

    // Set up artwork metadata based on UIImage
    private func setupArtworkMetadata(_ artwork: AVMutableMetadataItem) {
        if let image = UIImage(named: "AppLogo") {
            if let imageData = image.pngData() {
                artwork.identifier = .commonIdentifierArtwork
                artwork.value = imageData as NSData
                artwork.dataType = kCMMetadataBaseDataType_JPEG as String
                artwork.extendedLanguageTag = "und"
            }
        }
    }

    // MARK: - AVPlayerViewController Setup

    private func setupAVPlayerViewController() {
        avPlayerViewController.player = player
        avPlayerViewController.allowsPictureInPicturePlayback = true
        avPlayerViewController.canStartPictureInPictureAutomaticallyFromInline = true // auto start PIP when app is put to background

        // Hide controls in main player
        avPlayerViewController.showsPlaybackControls = false

        // Prevents seeking
        avPlayerViewController.requiresLinearPlayback = true

        // Setup PIP so we can close it if active
//        setupPIPController()
    }

//    private var pipController: AVPictureInPictureController?
//    private var displayObservation: NSKeyValueObservation?
//    private func setupPIPController() {
//        // Observe when AVPlayerViewController is ready
//        displayObservation = avPlayerViewController.observe(\.isReadyForDisplay, options: [.new]) { [weak self] controller, change in
//            guard let self, controller.isReadyForDisplay else { return }
//
//            // Try to get internal player layer now that it's available
//            if AVPictureInPictureController.isPictureInPictureSupported(),
//               let playerLayer = self.findPlayerLayer(in: controller.view) {
//                print("✅ PiP controller initialized")
//                self.pipController = AVPictureInPictureController(playerLayer: playerLayer)
//                self.pipController?.delegate = self
//            } else {
//                print("❌ Could not initialize PiP controller")
//            }
//
//            // Stop observing to avoid redundant calls
//            self.displayObservation?.invalidate()
//            self.displayObservation = nil
//        }
//    }
//    private func findPlayerLayer(in view: UIView) -> AVPlayerLayer? {
//        // Recursively search for AVPlayerLayer
//        if let layer = view.layer as? AVPlayerLayer {
//            return layer
//        }
//
//        for subview in view.subviews {
//            if let playerLayer = findPlayerLayer(in: subview) {
//                return playerLayer
//            }
//        }
//        return nil
//    }

    // MARK: - Playback Control

    func pausePlayer() {
        player?.pause()
    }

    func playPlayer() {
        player?.play()
    }

//    func startPIP() {
//        pipController?.startPictureInPicture()
//    }

    // NOTE: doesn't work if we are using canStartPictureInPictureAutomaticallyFromInline=true
    //       but without it, PIP doesn't auto start when app is put to background
    func stopPIP() {
        print("Stop PIP")
//        if let pip = pipController {
//            Task { @MainActor in
//                print("calling")
//                pip.stopPictureInPicture()
//            }
//        }
    }
}

// extension WidgetTutorialPlayerController: AVPictureInPictureControllerDelegate {
//    func pictureInPictureControllerWillStopPictureInPicture(_ pictureInPictureController: AVPictureInPictureController) {
//        print("🔻 Will stop PiP")
//    }
//
//    func pictureInPictureControllerDidStopPictureInPicture(_ pictureInPictureController: AVPictureInPictureController) {
//        print("✅ Did stop PiP")
//    }
//
//    func pictureInPictureController(_ pictureInPictureController: AVPictureInPictureController, failedToStartPictureInPictureWithError error: Error) {
//        print("❌ PiP failed to start: \(error.localizedDescription)")
//    }
//
//    func pictureInPictureController(_ pictureInPictureController: AVPictureInPictureController, restoreUserInterfaceForPictureInPictureStopWithCompletionHandler completionHandler: @escaping (Bool) -> Void) {
//        print("🧩 restore UI after PiP")
//        completionHandler(true)
//    }
// }
