//
//  WidgetTutorialView.swift
//  Appio
//
//  Created by gondo on 13/04/2025.
//

import SwiftUI

struct WidgetTutorialView: View {
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.scenePhase) private var scenePhase
    @Environment(\.dismiss) private var dismiss
    @StateObject var playerController = WidgetTutorialPlayerController()
    @State private var indicatorOpacity = 0.0

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Spacer()

                Button {
                    dismiss()
                } label: {
                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.gray)
                        .padding(8)
                        .background(.ultraThinMaterial)
                        .clipShape(Circle())
                }
                .padding()
                .accessibilityLabel("Dismiss")
            }

            GeometryReader { geometry in
                let videoMultiplier: CGFloat = 880 / 880
                let divider: CGFloat = geometry.size.width > geometry.size.height ? 1.5 : 1.2
                let videoWidth = geometry.size.width / divider
                let cornerRadius = videoWidth / 6

                WidgetTutorialPlayer(playerController: playerController)
                    .frame(width: videoWidth, height: videoWidth * videoMultiplier)
                    .cornerRadius(cornerRadius)
//                    .clipShape(
//                        .rect(topLeadingRadius: cornerRadius, bottomLeadingRadius: 16, bottomTrailingRadius: 16, topTrailingRadius: cornerRadius)
//                    )
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            }
            .padding(.bottom, 30)

            Spacer()

            ArrowAnimation()
                .frame(height: 30)
                .padding(.bottom, 24)
                .opacity(indicatorOpacity)

            Text("SWIPE UP")
                .font(.system(size: 88, weight: .light))
                .foregroundStyle(.tertiary)
                .fontWidth(.compressed)
                .opacity(indicatorOpacity)
        }
        .onAppear {
            playerController.initPlayer(colorScheme: colorScheme)
            playerController.playPlayer()

            withAnimation(.easeOut(duration: 3).delay(5)) {
                indicatorOpacity = 1
            }
        }
        .onChange(of: scenePhase) { _, newValue in
            if newValue == .active {
                // Restart in case video was stopped in PIP
                playerController.playPlayer()
                playerController.stopPIP()
            }
        }
    }
}

#Preview {
    WidgetTutorialView()
}
