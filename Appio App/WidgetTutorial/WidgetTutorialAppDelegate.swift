//
//  WidgetTutorialAppDelegate.swift
//  Appio
//
//  Created by gondo on 14/04/2025.
//

import AVFoundation

enum WidgetTutorialAppDelegate {
    static func registerPIP() {
        let audioSession = AVAudioSession.sharedInstance()

        do {
            try audioSession.setCategory(AVAudioSession.Category.playback, options: [AVAudioSession.CategoryOptions.mixWithOthers])
            try audioSession.setActive(true)
        } catch {
            print("Audio session failed")
        }
    }
}
