//
//  StringExtension.swift
//  Appio
//
//  Created by gondo on 29/03/2025.
//

extension String {
    func truncated(to length: Int) -> String {
        return count > length ? String(prefix(length)) : self // …
    }

    func truncatedHumanReadable(to length: Int, trailing: String = "…") -> String {
        // If the string is already short, return it as is.
        guard count > length else { return self }

        // Get the substring up to the specified length.
        let indexAtLength = index(startIndex, offsetBy: length)
        var truncated = String(self[..<indexAtLength])

        // Find the last whitespace to avoid cutting a word in half.
        if let lastSpaceIndex = truncated.lastIndex(of: " ") {
            truncated = String(truncated[..<lastSpaceIndex])
        }

        return truncated + trailing
    }
}
