//
//  DateExtension.swift
//  Appio
//
//  Created by gondo on 13/03/2025.
//

import Foundation

extension Date {
    static var nowUTC: Date {
        let calendar = Calendar(identifier: .gregorian)
        var utcCalendar = calendar
        utcCalendar.timeZone = TimeZone(identifier: "UTC")!
        return .now.convertToTimeZone(utcCalendar.timeZone)
    }

    private func convertToTimeZone(_ timeZone: TimeZone) -> Date {
        let seconds = TimeInterval(timeZone.secondsFromGMT(for: self))
        return Date(timeInterval: seconds, since: self)
    }

    func humanReadable(separator: String = "\n") -> String {
        let calendar = Calendar.current
        if calendar.isDateInToday(self) {
            return formatted(.dateTime.hour().minute())
        } else if calendar.isDateInYesterday(self) {
            return "Yesterday" + separator + formatted(.dateTime.hour().minute())
        } else {
            return formatted(.dateTime.month().day()) + separator + formatted(.dateTime.hour().minute())
        }
    }

    var humanReadableTime: String {
        return formatted(.dateTime.hour().minute().second())
    }
}
