//
//  UIDeviceExtension.swift
//  Appio
//
//  Created by gondo on 16/04/2025.
//

import UIKit

extension UIDevice {
    var screenSize: CGSize {
        UIScreen.main.bounds.size
    }

    var deviceIdentifier: String {
        var systemInfo = utsname()
        uname(&systemInfo)

        return withUnsafePointer(to: &systemInfo.machine) {
            $0.withMemoryRebound(to: CChar.self, capacity: 1) {
                String(cString: $0)
            }
        }
    }

    var userInterInterface: String {
        switch UIDevice.current.userInterfaceIdiom {
        case .phone:
            return "iPhone"
        case .pad:
            return "iPad"
        case .tv:
            return "Apple TV"
        case .carPlay:
            return "CarPlay"
        default:
            return "Unknown"
        }
    }
}
