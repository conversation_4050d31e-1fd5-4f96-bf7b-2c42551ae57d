//
//  ColorExtension.swift
//  Appio App
//
//  Created by gondo on 24/10/2024.
//

import SwiftUI

extension Color {
    init?(_ hex: String) {
        var value = hex
        if value.first == "#" {
            value = String(value.dropFirst())
        }

        var int: UInt64 = 0
        Scanner(string: value).scanHexInt64(&int)

        let a, r, g, b: UInt64
        switch value.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    init?(hex: String) {
        self.init(hex)
    }

//    var hex: String {
//        var r: CGFloat = 0
//        var g: CGFloat = 0
//        var b: CGFloat = 0
//        var a: CGFloat = 0
//        getRed(&r, green: &g, blue: &b, alpha: &a)
//        return String(format: "%02X%02X%02X%02X", Int(r * 0xFF), Int(g * 0xFF), Int(b * 0xFF), Int(a * 0xFF))
//    }
}
