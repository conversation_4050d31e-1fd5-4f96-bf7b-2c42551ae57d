////
////  CustomSheetInternalModifier.swift
////  Appio
////
////  Created by gondo on 10/03/2025.
////
//
// import SwiftUI
//
// struct CustomSheetInternalModifier: ViewModifier {
//    private let padding: CGFloat = 5
//    private let cornerRadius: CGFloat = CornerRadiusByDevice.get()
//
//    @State private var offset: CGFloat = 0
//    var dismissAction: CustomDismissAction
//
//    func body(content: Content) -> some View {
//        content
//            .frame(maxWidth: .infinity, maxHeight: .infinity)
//            .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
//            .background(Color(UIColor.systemGroupedBackground), in: RoundedRectangle(cornerRadius: cornerRadius))
//            .padding(padding)
////            .gesture(
////                DragGesture(coordinateSpace: .global)
////                    .onChanged { value in
////                        offset = clip (
////                            value: value.translation.height,
////                            lower: -30,
////                            upper: .infinity
////                        )
////                    }
////                    .onEnded { value in
////                        if value.predictedEndTranslation.height > 100 {
////                            dismissAction()
////                        }
////                        offset = 0
////                    }
////            )
//            .zIndex(1)
//            .offset(y: offset)
//            .animation(.interactiveSpring(), value: offset)
//            .transition(.move(edge: .bottom))
//            .environment(\.customIsPresented, true)
//            .environment(\.customDismiss, dismissAction)
//
//    }
// }
//
// enum CornerRadiusByDevice {
//    static func get() -> CGFloat {
//        switch UIDevice.current.model {
//        case "iPad", "iPad Air", "iPad Pro", "iPad mini":
//            return 30
//        case "iPhone":
//            return 60
////        case "iPod touch":
////            return 30 // iPod touch
////        case "Apple TV":
////            return 15 // Apple TV
////        case "Apple Watch":
////            return 12 // Apple Watch
////        case "Mac", "MacBook", "MacBook Pro", "MacBook Air", "iMac", "Mac mini", "Mac Pro":
////            return 10 // Mac devices
////        case "HomePod":
////            return 25 // HomePod
//        default:
//            return 50
//        }
//    }
// }
//
// struct CustomSheet<SheetContent: View>: ViewModifier {
//    @Binding var isPresented: Bool
//    var dismiss: (() -> Void)?
//    @ViewBuilder var sheetContent: () -> SheetContent
//
//    init(isPresented: Binding<Bool>, onDismiss dismiss: (() -> Void)? = nil, sheetContent: @escaping () -> SheetContent) {
//        self._isPresented = isPresented
//        self.dismiss = dismiss
//        self.sheetContent = sheetContent
//    }
//
//    func body(content: Content) -> some View {
//        ZStack(alignment: .bottom) {
//            content
//
//            if isPresented {
//                Color.black.opacity(0.3)
//                    .ignoresSafeArea()
//                    .zIndex(0)
//
//                sheetContent()
//                    .modifier(CustomSheetInternalModifier(dismissAction: CustomDismissAction { isPresented = false }))
//            }
//        }
//        .animation(.snappy(duration: 0.3), value: isPresented)
//        .ignoresSafeArea(.all, edges: .bottom)
//        .onChange(of: isPresented) { newValue in
//            if !newValue {
//                dismiss?()
//            }
//        }
//    }
// }
//
// struct CustomItemSheet<Item: Identifiable, SheetContent: View>: ViewModifier {
//    @Binding var item: Item?
//    var dismiss: (() -> Void)?
//    @ViewBuilder var sheetContent: (Item) -> SheetContent
//
//    init(item: Binding<Item?>, onDismiss dismiss: (() -> Void)? = nil, sheetContent: @escaping (Item) -> SheetContent) {
//        self._item = item
//        self.dismiss = dismiss
//        self.sheetContent = sheetContent
//    }
//
//    var isPresented: Bool {
//        item != nil
//    }
//
//    func body(content: Content) -> some View {
//        ZStack(alignment: .bottom) {
//            content
//
//            if let item {
//                Color.black.opacity(0.3)
//                    .ignoresSafeArea()
//                    .zIndex(0)
//
//                sheetContent(item)
//                    .modifier(CustomSheetInternalModifier(dismissAction: CustomDismissAction { self.item = nil }))
//            }
//        }
//        .animation(.snappy(duration: 0.3), value: isPresented)
//        .ignoresSafeArea(.all, edges: .bottom)
//        .onChange(of: isPresented) { newValue in
//            if !newValue {
//                dismiss?()
//            }
//        }
//    }
// }
//
// extension View {
//    func customSheet<SheetContent>(
//        isPresented: Binding<Bool>,
//        onDismiss: (() -> Void)? = nil,
//        @ViewBuilder sheetContent: @escaping () -> SheetContent
//    ) -> some View where SheetContent: View {
//        modifier(
//            CustomSheet(
//                isPresented: isPresented,
//                onDismiss: onDismiss,
//                sheetContent: sheetContent
//            )
//        )
//    }
//
//    func customSheet<Item, SheetContent>(
//        item: Binding<Item?>,
//        onDismiss: (() -> Void)? = nil,
//        @ViewBuilder sheetContent: @escaping (Item) -> SheetContent
//    ) -> some View where Item: Identifiable, SheetContent: View {
//        modifier(
//            CustomItemSheet(
//                item: item,
//                onDismiss: onDismiss,
//                sheetContent: sheetContent
//            )
//        )
//    }
// }
