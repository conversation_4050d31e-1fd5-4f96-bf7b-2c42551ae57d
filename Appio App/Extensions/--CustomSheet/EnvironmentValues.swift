////
////  EnvironmentValues.swift
////  Appio
////
////  Created by gondo on 10/03/2025.
////
//
// import SwiftUI
//
// private struct CustomIsPresentedKey: EnvironmentKey {
//    static let defaultValue: Bool = false
// }
//
// private struct DismissCustomSheetKey: EnvironmentKey {
//    static let defaultValue = CustomDismissAction()
// }
//
// public struct CustomDismissAction {
//    var dismiss: (() -> Void)?
//
//    func callAsFunction() {
//        dismiss?()
//    }
// }
//
// extension EnvironmentValues {
//    var customDismiss: CustomDismissAction {
//        get { self[DismissCustomSheetKey.self] }
//        set { self[DismissCustomSheetKey.self] = newValue }
//    }
//
//    var customIsPresented: Bool {
//        get { self[CustomIsPresentedKey.self] }
//        set { self[CustomIsPresentedKey.self] = newValue }
//    }
// }
