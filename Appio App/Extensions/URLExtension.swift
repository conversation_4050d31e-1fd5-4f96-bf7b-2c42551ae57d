//
//  URLExtension.swift
//  Appio
//
//  Created by gondo on 11/04/2025.
//

import SwiftUI

extension URL {
    func getQueryItem(key: String, decode: Bool = true) -> String? {
        guard let components = URLComponents(url: self, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems,
              let itemUrl = queryItems.first(where: { $0.name == key }),
              let encodedValueUrl = itemUrl.value
        else {
            return nil
        }

        return decode ? encodedValueUrl.removingPercentEncoding : encodedValueUrl
    }
}
