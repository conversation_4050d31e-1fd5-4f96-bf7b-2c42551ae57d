//
//  DeviceEntity.swift
//  Appio
//
//  Created by gondo on 04/03/2025.
//

import Foundation
import UIKit

struct DeviceEntity: Codable, Equatable {
    let id: String

    var osVersion: String = UIDevice.current.systemVersion {
        willSet {
            if newValue != osVersion {
                lastUpdate = .now // requires storing after update
            }
        }
    }

    // In case user updates device and app's data are persisted
    var deviceIdentifier: String = UIDevice.current.deviceIdentifier {
        willSet {
            if newValue != deviceIdentifier {
                lastUpdate = .now // requires storing after update
            }
        }
    }

    private var lastSync: Date = .now // when was the entity last time successfully synced via API
    private var lastUpdate: Date = .now // when was the entity locally updated for the last time

    var notifiationsEnabled: Bool = false {
        willSet {
            if newValue != notifiationsEnabled {
                lastUpdate = .now // requires storing after update
            }
        }
    }

    var apnToken: String = "" {
        willSet {
            if newValue != apnToken {
                lastUpdate = .now // requires storing after update
            }
        }
    }

    func isOutOfSync() -> Bool {
        return lastUpdate > lastSync
    }

    mutating func markSynced() {
        lastSync = .now // requires storing after update
    }

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id, osVersion, deviceIdentifier, apnToken, notifiationsEnabled, lastSync, lastUpdate
    }
}

extension DeviceEntity {
    init(from response: DeviceResponse) {
        id = response.id
        osVersion = UIDevice.current.systemVersion
        deviceIdentifier = UIDevice.current.deviceIdentifier
    }
}

#if DEBUG
    extension DeviceEntity {
        static var mock: DeviceEntity {
            return DeviceEntity(
                id: "dvc_00000000000000000000000000",
            )
        }
    }
#endif
