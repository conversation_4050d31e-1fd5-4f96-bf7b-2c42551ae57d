//
//  NotificationEntity.swift
//  Appio
//
//  Created by gondo on 11/03/2025.
//

import Foundation

// no status as all the notificatiosn on ios are delivered
struct NotificationEntity: Codable, Equatable, Identifiable, Hashable {
    let id: String
    let serviceId: String
    let payload: NotificationEntityPayload
    let receivedAt: Date
    private(set) var linkOpenedAt: Date? // - NOT NEEDED, can delete without impacting real data?

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case id, serviceId, payload, receivedAt, linkOpenedAt
    }
}

extension NotificationEntity {
    init(from response: NotificationResponse) {
        id = response.id
        serviceId = response.serviceId
        payload = NotificationEntityPayload(from: response.payload)
        receivedAt = .now
    }
}

struct NotificationEntityPayload: Codable, Equatable, Hashable {
    let title: String
    let subtitle: String
    let message: String
    var link: String?
    var imageUrl: String?

    // Custom mapping of JSON keys to struct properties
    enum CodingKeys: String, CodingKey {
        case title, subtitle, message, link, imageUrl = "image_url"
    }
}

extension NotificationEntityPayload {
    init(from response: NotificationEntityPayloadResponse) {
        title = response.title
        subtitle = response.subtitle
        message = response.message
        link = response.link
        imageUrl = response.imageUrl
    }
}

#if DEBUG
    extension NotificationEntity {
        func clone(with newId: String) -> NotificationEntity {
            return NotificationEntity(
                id: newId,
                serviceId: serviceId,
                payload: payload,
                receivedAt: receivedAt,
                linkOpenedAt: linkOpenedAt
            )
        }
    }

    extension NotificationEntity {
        static var mock: NotificationEntity {
            return NotificationEntity(
                id: "ntf_00000000000000000000000000",
                serviceId: "svc_00000000000000000000000000",
                payload: NotificationEntityPayload(
                    title: "Service Name A",
                    subtitle: "Widgets now live!",
                    message: "Mock: Service update: You can now add a widget with your most critical metrics to your home screen.",
                    link: "https://appio.so/"
                ),
                receivedAt: .now,
                linkOpenedAt: .now
            )
        }

        static var mockB: NotificationEntity {
            return NotificationEntity(
                id: "ntf_00000000000000001111111111",
                serviceId: "svc_00000000000000000000000000",
                payload: NotificationEntityPayload(
                    title: "Second Service Name B",
                    subtitle: "Second Notification Title Pellentesque at ex et lectus dignissim sodales sit amet at nibh",
                    message: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent maximus sapien vitae risus fringilla pretium. Nunc ut diam metus. Suspendisse ornare sodales sem sed maximus. Suspendisse vitae consequat lacus. Morbi tempor tristique luctus. Vestibulum molestie consequat mi, volutpat tempor metus dignissim vehicula. Ut malesuada fermentum orci ac porta. Proin ac tellus id urna porttitor pulvinar sit amet sit amet tortor. Nulla a tortor scelerisque, laoreet sapien dignissim, venenatis nisi. Integer at ligula mollis felis condimentum lobortis ut non mi. Sed porttitor porta ultricies. Integer consequat tincidunt elit, vel vehicula dolor.",
                    link: "https://appio.so/",
                    imageUrl: "https://cdn.appio.so/app/demo.appio.so/banner.jpg"
                ),
                receivedAt: Date().addingTimeInterval(-86400),
                linkOpenedAt: Date().addingTimeInterval(-86400)
            )
        }

        static var mockC: NotificationEntity {
            return NotificationEntity(
                id: "ntf_00000000000000002222222222",
                serviceId: "svc_00000000000000000000000000",
                payload: NotificationEntityPayload(
                    title: "Service Name C",
                    subtitle: "🤑 SALE!!! 🤑",
                    message: "Shop now! We are introducing an insane 50% discount on all our products 🤯"
                ),
                receivedAt: Date().addingTimeInterval(-86400 * 7),
                linkOpenedAt: Date().addingTimeInterval(-86400 * 7)
            )
        }

        static var mockD: NotificationEntity {
            return NotificationEntity(
                id: "ntf_00000000000000000000000001",
                serviceId: "svc_00000000000000000000000000",
                payload: NotificationEntityPayload(
                    title: "Service Name D",
                    subtitle: "Introducing Appio.so",
                    message: "Appio helps businesses add mobile features (like notifications and widgets) without the need to build or maintain an app, or deal with app store approvals."
                ),
                receivedAt: Date().addingTimeInterval(-86400 * 7),
                linkOpenedAt: Date().addingTimeInterval(-86400 * 7)
            )
        }

        static var mockE: NotificationEntity {
            return mockB.clone(with: "ntf_00000000000000000000000002")
        }

        static var mockF: NotificationEntity {
            return mockC.clone(with: "ntf_00000000000000000000000003")
        }

        static var mockG: NotificationEntity {
            return mock.clone(with: "ntf_00000000000000000000000004")
        }

        static var mockH: NotificationEntity {
            return mockB.clone(with: "ntf_00000000000000000000000005")
        }

        static var mockI: NotificationEntity {
            return mockC.clone(with: "ntf_00000000000000000000000006")
        }
    }
#endif
