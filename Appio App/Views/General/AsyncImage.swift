//
//  AsyncImage.swift
//  Appio App
//
//  Created by gondo on 09/07/2023.
//

import SwiftUI

struct AsyncImageView: View {
    let url: URL?
    let contentMode: ContentMode
    let cornerRadius: CGFloat

    init(
        urlString: String?,
        contentMode: ContentMode = .fill,
        cornerRadius: CGFloat = 0
    ) {
        url = URL(string: urlString ?? "")
        self.contentMode = contentMode
        self.cornerRadius = cornerRadius
    }

    var body: some View {
        GeometryReader { _ in
            if let url = url {
                CachedAsyncImage(url: url) { phase in
                    switch phase {
                    case let .success(image):
                        if cornerRadius > 0 {
                            image.resizable()
                                .aspectRatio(contentMode: contentMode)
                                .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                        } else {
                            image.resizable()
                                .aspectRatio(contentMode: contentMode)
                        }
                    case .failure:
                        ImagePlaceholderView(cornerRadius: cornerRadius)
                    case .empty:
                        LoadingViewView(cornerRadius: cornerRadius)
                    @unknown default:
                        ImagePlaceholderView(cornerRadius: cornerRadius)
                    }
                }
            } else {
                ImagePlaceholderView(cornerRadius: cornerRadius)
            }
        }
    }
}

private struct CachedAsyncImage<Content: View>: View {
    let url: URL
    @ViewBuilder let content: (AsyncImagePhase) -> Content

    @State private var image: UIImage?
    @State private var isLoading = false
    @State private var loadError: Error?

    var body: some View {
        ZStack {
            Color.clear // fill the space

            Group {
                if let image = image {
                    content(.success(Image(uiImage: image)))
                } else if isLoading {
                    content(.empty)
                } else if loadError != nil {
                    content(.failure(loadError!))
                } else {
                    content(.empty)
                }
            }
        }
        .task {
            await loadImage()
        }
    }

    private func loadImage() async {
        // First check if image is already cached synchronously
        if let cachedImage = ImageManager.loadImageFromCacheSync(url) {
            await MainActor.run {
                self.image = cachedImage
            }
            return
        }

        // If not cached, show loading state and download
        await MainActor.run {
            self.isLoading = true
        }

        do {
            let downloadedImage = try await ImageManager.shared.getImage(for: url)
            await MainActor.run {
                self.image = downloadedImage
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.loadError = error
                self.isLoading = false
            }
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // Banner style
        AsyncImageView(urlString: "https://picsum.photos/1600/900")
            .aspectRatio(16 / 9, contentMode: .fit)
            .frame(width: 300)

        // Logo style
        AsyncImageView(urlString: "https://picsum.photos/200", contentMode: .fit, cornerRadius: 15)
            .frame(width: 48, height: 48)

        // Failed loading
        AsyncImageView(urlString: "invalid-url", cornerRadius: 10)
            .frame(width: 100, height: 100)
    }
    .padding()
}
