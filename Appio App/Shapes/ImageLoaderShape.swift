//
//  ImageLoaderShape.swift
//  Appio
//
//  Created by gondo on 13/03/2025.
//

import SwiftUI

struct ImageLoaderShape: View {
    private let size = CGSize(width: 20, height: 20)
    private var color: Color

    init(color: Color) {
        self.color = color
    }

    struct RightMountain: View {
        var color: Color

        struct PathShape1: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 4.7194, y: 0.4156))
                    path.addCurve(to: CGPoint(x: 3.3746, y: 0.4156),
                                  control1: CGPoint(x: 4.4423, y: -0.1385),
                                  control2: CGPoint(x: 3.6516, y: -0.1385))
                    path.addCurve(to: CGPoint(x: 0.0803, y: 7.0039),
                                  control1: CGPoint(x: 2.2765, y: 2.6117),
                                  control2: CGPoint(x: 1.1784, y: 4.8078))
                    path.addCurve(to: CGPoint(x: 0.7527, y: 8.0919),
                                  control1: CGPoint(x: -0.1696, y: 7.5038),
                                  control2: CGPoint(x: 0.1939, y: 8.0919))
                    path.addCurve(to: CGPoint(x: 7.3412, y: 8.0919),
                                  control1: CGPoint(x: 2.9489, y: 8.0919),
                                  control2: CGPoint(x: 5.145, y: 8.0919))
                    path.addCurve(to: CGPoint(x: 8.0137, y: 7.0039),
                                  control1: CGPoint(x: 7.9001, y: 8.0919),
                                  control2: CGPoint(x: 8.2636, y: 7.5038))
                    path.addCurve(to: CGPoint(x: 4.7194, y: 0.4156),
                                  control1: CGPoint(x: 6.9156, y: 4.8078),
                                  control2: CGPoint(x: 5.8175, y: 2.6117))
                    path.addCurve(to: CGPoint(x: 4.7194, y: 0.4156),
                                  control1: CGPoint(x: 4.7194, y: 0.4156),
                                  control2: CGPoint(x: 4.7194, y: 0.4156))
                    path.addCurve(to: CGPoint(x: 4.7194, y: 0.4156),
                                  control1: CGPoint(x: 4.7194, y: 0.4156),
                                  control2: CGPoint(x: 4.7194, y: 0.4156))
                }
            }
        }

        var body: some View {
            PathShape1()
                .fill(color)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: -2.0937, ty: -4.2561))
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10.1707))
        }
    }

    struct LeftMountain: View {
        var color: Color

        struct PathShape2: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 3.2158, y: 0.4156))
                    path.addCurve(to: CGPoint(x: 1.871, y: 0.4156),
                                  control1: CGPoint(x: 2.9388, y: -0.1385),
                                  control2: CGPoint(x: 2.1481, y: -0.1385))
                    path.addCurve(to: CGPoint(x: 0.0803, y: 3.9969),
                                  control1: CGPoint(x: 1.2741, y: 1.6093),
                                  control2: CGPoint(x: 0.6772, y: 2.8031))
                    path.addCurve(to: CGPoint(x: 0.7527, y: 5.0848),
                                  control1: CGPoint(x: -0.1696, y: 4.4967),
                                  control2: CGPoint(x: 0.1939, y: 5.0848))
                    path.addCurve(to: CGPoint(x: 4.3341, y: 5.0848),
                                  control1: CGPoint(x: 1.9465, y: 5.0848),
                                  control2: CGPoint(x: 3.1403, y: 5.0848))
                    path.addCurve(to: CGPoint(x: 5.0065, y: 3.9969),
                                  control1: CGPoint(x: 4.893, y: 5.0848),
                                  control2: CGPoint(x: 5.2564, y: 4.4967))
                    path.addCurve(to: CGPoint(x: 3.2158, y: 0.4156),
                                  control1: CGPoint(x: 4.4096, y: 2.8031),
                                  control2: CGPoint(x: 3.8127, y: 1.6093))
                    path.addCurve(to: CGPoint(x: 3.2158, y: 0.4156),
                                  control1: CGPoint(x: 3.2158, y: 0.4156),
                                  control2: CGPoint(x: 3.2158, y: 0.4156))
                    path.addCurve(to: CGPoint(x: 3.2158, y: 0.4156),
                                  control1: CGPoint(x: 3.2158, y: 0.4156),
                                  control2: CGPoint(x: 3.2158, y: 0.4156))
                }
            }
        }

        var body: some View {
            PathShape2()
                .fill(color)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: -6.0002, ty: -1.2443))
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10.1707))
        }
    }

    struct BottomMountain: View {
        var color: Color

        struct PathShape3: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addCurve(to: CGPoint(x: 4.1885, y: 0),
                                  control1: CGPoint(x: 1.3962, y: 0),
                                  control2: CGPoint(x: 2.7923, y: 0))
                    path.addCurve(to: CGPoint(x: 4.1885, y: 0.3222),
                                  control1: CGPoint(x: 4.1885, y: 0.1074),
                                  control2: CGPoint(x: 4.1885, y: 0.2148))
                    path.addCurve(to: CGPoint(x: 0, y: 0.3222),
                                  control1: CGPoint(x: 2.7923, y: 0.3222),
                                  control2: CGPoint(x: 1.3962, y: 0.3222))
                    path.addCurve(to: CGPoint(x: 0, y: 0),
                                  control1: CGPoint(x: 0, y: 0.2148),
                                  control2: CGPoint(x: 0, y: 0.1074))
                    path.addCurve(to: CGPoint(x: 0, y: 0),
                                  control1: CGPoint(x: 0, y: 0),
                                  control2: CGPoint(x: 0, y: 0))
                    path.addCurve(to: CGPoint(x: 0, y: 0),
                                  control1: CGPoint(x: 0, y: 0),
                                  control2: CGPoint(x: 0, y: 0))
                }
            }
        }

        var body: some View {
            PathShape3()
                .fill(color)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: -2.9004, ty: 3.5174))
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10.1707))
        }
    }

    struct Square: View {
        var color: Color

        struct PathShape4: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 9, y: -9))
                    path.addCurve(to: CGPoint(x: 9, y: 9),
                                  control1: CGPoint(x: 9, y: -9),
                                  control2: CGPoint(x: 9, y: 9))
                    path.addCurve(to: CGPoint(x: -9, y: 9),
                                  control1: CGPoint(x: 9, y: 9),
                                  control2: CGPoint(x: -9, y: 9))
                    path.addCurve(to: CGPoint(x: -9, y: -9),
                                  control1: CGPoint(x: -9, y: 9),
                                  control2: CGPoint(x: -9, y: -9))
                    path.addCurve(to: CGPoint(x: 9, y: -9),
                                  control1: CGPoint(x: -9, y: -9),
                                  control2: CGPoint(x: 9, y: -9))
                    path.closeSubpath()
                }
            }
        }

        var body: some View {
            PathShape4()
                .fill(color)
                .transformEffect(CGAffineTransform.identity)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10))
        }
    }

    struct Sun: View {
        var color: Color

        struct PathShape5: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 0, y: -1.5035))
                    path.addCurve(to: CGPoint(x: 1.5035, y: 0),
                                  control1: CGPoint(x: 0.8298, y: -1.5035),
                                  control2: CGPoint(x: 1.5035, y: -0.8298))
                    path.addCurve(to: CGPoint(x: 0, y: 1.5035),
                                  control1: CGPoint(x: 1.5035, y: 0.8298),
                                  control2: CGPoint(x: 0.8298, y: 1.5035))
                    path.addCurve(to: CGPoint(x: -1.5035, y: 0),
                                  control1: CGPoint(x: -0.8298, y: 1.5035),
                                  control2: CGPoint(x: -1.5035, y: 0.8298))
                    path.addCurve(to: CGPoint(x: 0, y: -1.5035),
                                  control1: CGPoint(x: -1.5035, y: -0.8298),
                                  control2: CGPoint(x: -0.8298, y: -1.5035))
                    path.closeSubpath()
                }
            }
        }

        var body: some View {
            PathShape5()
                .fill(color)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: -2.2, ty: -3.5077))
            //                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10))
        }
    }

    struct Moon: View {
        var color: Color

        struct PathShape6: Shape {
            func path(in _: CGRect) -> Path {
                Path { path in
                    path.move(to: CGPoint(x: 2.6603, y: 2.2503))
                    path.addCurve(to: CGPoint(x: 2.9426, y: 2.2253),
                                  control1: CGPoint(x: 2.7568, y: 2.2503),
                                  control2: CGPoint(x: 2.8509, y: 2.242))
                    path.addCurve(to: CGPoint(x: 3.1641, y: 2.1595),
                                  control1: CGPoint(x: 3.0343, y: 2.2087),
                                  control2: CGPoint(x: 3.1081, y: 2.1868))
                    path.addCurve(to: CGPoint(x: 3.3642, y: 2.1007),
                                  control1: CGPoint(x: 3.2392, y: 2.1203),
                                  control2: CGPoint(x: 3.3059, y: 2.1007))
                    path.addCurve(to: CGPoint(x: 3.4625, y: 2.1399),
                                  control1: CGPoint(x: 3.4059, y: 2.1007),
                                  control2: CGPoint(x: 3.4387, y: 2.1138))
                    path.addCurve(to: CGPoint(x: 3.5, y: 2.2449),
                                  control1: CGPoint(x: 3.4875, y: 2.166),
                                  control2: CGPoint(x: 3.5, y: 2.201))
                    path.addCurve(to: CGPoint(x: 3.4875, y: 2.3304),
                                  control1: CGPoint(x: 3.5, y: 2.2698),
                                  control2: CGPoint(x: 3.4958, y: 2.2983))
                    path.addCurve(to: CGPoint(x: 3.4518, y: 2.4336),
                                  control1: CGPoint(x: 3.4804, y: 2.3624),
                                  control2: CGPoint(x: 3.4684, y: 2.3968))
                    path.addCurve(to: CGPoint(x: 3.1748, y: 2.8716),
                                  control1: CGPoint(x: 3.3803, y: 2.595),
                                  control2: CGPoint(x: 3.288, y: 2.741))
                    path.addCurve(to: CGPoint(x: 2.7907, y: 3.208),
                                  control1: CGPoint(x: 3.0629, y: 3.0021),
                                  control2: CGPoint(x: 2.9348, y: 3.1143))
                    path.addCurve(to: CGPoint(x: 2.3226, y: 3.4252),
                                  control1: CGPoint(x: 2.6466, y: 3.303),
                                  control2: CGPoint(x: 2.4906, y: 3.3754))
                    path.addCurve(to: CGPoint(x: 1.8027, y: 3.5),
                                  control1: CGPoint(x: 2.1559, y: 3.4751),
                                  control2: CGPoint(x: 1.9826, y: 3.5))
                    path.addCurve(to: CGPoint(x: 1.0845, y: 3.3665),
                                  control1: CGPoint(x: 1.5431, y: 3.5),
                                  control2: CGPoint(x: 1.3036, y: 3.4555))
                    path.addCurve(to: CGPoint(x: 0.511, y: 2.9926),
                                  control1: CGPoint(x: 0.8653, y: 3.2787),
                                  control2: CGPoint(x: 0.6741, y: 3.154))
                    path.addCurve(to: CGPoint(x: 0.134, y: 2.4229),
                                  control1: CGPoint(x: 0.349, y: 2.8312),
                                  control2: CGPoint(x: 0.2233, y: 2.6413))
                    path.addCurve(to: CGPoint(x: 0, y: 1.7073),
                                  control1: CGPoint(x: 0.0447, y: 2.2046),
                                  control2: CGPoint(x: 0, y: 1.966))
                    path.addCurve(to: CGPoint(x: 0.1394, y: 1.013),
                                  control1: CGPoint(x: 0, y: 1.464),
                                  control2: CGPoint(x: 0.0465, y: 1.2325))
                    path.addCurve(to: CGPoint(x: 0.5306, y: 0.429),
                                  control1: CGPoint(x: 0.2323, y: 0.7922),
                                  control2: CGPoint(x: 0.3627, y: 0.5976))
                    path.addCurve(to: CGPoint(x: 1.122, y: 0.0374),
                                  control1: CGPoint(x: 0.6998, y: 0.2593),
                                  control2: CGPoint(x: 0.8969, y: 0.1288))
                    path.addCurve(to: CGPoint(x: 1.2096, y: 0.0089),
                                  control1: CGPoint(x: 1.1542, y: 0.0243),
                                  control2: CGPoint(x: 1.1833, y: 0.0148))
                    path.addCurve(to: CGPoint(x: 1.2882, y: 0),
                                  control1: CGPoint(x: 1.2369, y: 0.003),
                                  control2: CGPoint(x: 1.2631, y: 0))
                    path.addCurve(to: CGPoint(x: 1.4007, y: 0.0427),
                                  control1: CGPoint(x: 1.3358, y: 0),
                                  control2: CGPoint(x: 1.3733, y: 0.0142))
                    path.addCurve(to: CGPoint(x: 1.4436, y: 0.1495),
                                  control1: CGPoint(x: 1.4293, y: 0.0712),
                                  control2: CGPoint(x: 1.4436, y: 0.1068))
                    path.addCurve(to: CGPoint(x: 1.4275, y: 0.2386),
                                  control1: CGPoint(x: 1.4436, y: 0.178),
                                  control2: CGPoint(x: 1.4382, y: 0.2077))
                    path.addCurve(to: CGPoint(x: 1.3864, y: 0.3329),
                                  control1: CGPoint(x: 1.418, y: 0.2682),
                                  control2: CGPoint(x: 1.4043, y: 0.2997))
                    path.addCurve(to: CGPoint(x: 1.3257, y: 0.4931),
                                  control1: CGPoint(x: 1.3626, y: 0.3768),
                                  control2: CGPoint(x: 1.3424, y: 0.4302))
                    path.addCurve(to: CGPoint(x: 1.2882, y: 0.6961),
                                  control1: CGPoint(x: 1.309, y: 0.5548),
                                  control2: CGPoint(x: 1.2965, y: 0.6225))
                    path.addCurve(to: CGPoint(x: 1.2757, y: 0.9151),
                                  control1: CGPoint(x: 1.2798, y: 0.7685),
                                  control2: CGPoint(x: 1.2757, y: 0.8415))
                    path.addCurve(to: CGPoint(x: 1.3703, y: 1.4723),
                                  control1: CGPoint(x: 1.2757, y: 1.1216),
                                  control2: CGPoint(x: 1.3072, y: 1.3073))
                    path.addCurve(to: CGPoint(x: 1.6473, y: 1.8942),
                                  control1: CGPoint(x: 1.4347, y: 1.6372),
                                  control2: CGPoint(x: 1.527, y: 1.7779))
                    path.addCurve(to: CGPoint(x: 2.0832, y: 2.1595),
                                  control1: CGPoint(x: 1.7676, y: 2.0093),
                                  control2: CGPoint(x: 1.9129, y: 2.0977))
                    path.addCurve(to: CGPoint(x: 2.6603, y: 2.2503),
                                  control1: CGPoint(x: 2.2547, y: 2.22),
                                  control2: CGPoint(x: 2.4471, y: 2.2503))
                    path.addCurve(to: CGPoint(x: 2.6603, y: 2.2503),
                                  control1: CGPoint(x: 2.6603, y: 2.2503),
                                  control2: CGPoint(x: 2.6603, y: 2.2503))
                    path.addCurve(to: CGPoint(x: 2.6603, y: 2.2503),
                                  control1: CGPoint(x: 2.6603, y: 2.2503),
                                  control2: CGPoint(x: 2.6603, y: 2.2503))
                }
            }
        }

        var body: some View {
            PathShape6()
                .fill(color)
                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: -3.75, ty: -5.5))
            //                .transformEffect(CGAffineTransform(a: 1, b: 0, c: 0, d: 1, tx: 10, ty: 10))
        }
    }

    @State private var angle: Double = 0 // Rotation angle in degrees

    private let rotationDuration: Double = 5.0 // Full rotation duration in seconds
    private let animationSize: CGFloat = 1.0 // Size for the rotating elements

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                RightMountain(color: color)
                LeftMountain(color: color)
                BottomMountain(color: color)
                Square(color: color).opacity(0)

                Sun(color: color)
                    .frame(width: animationSize, height: animationSize)
                    .position(x: size.width / 2, y: size.height / 2)
                    .rotationEffect(Angle.degrees(angle - 50)) // stating position is -50
                    .modifier(FadeWithRotation(rotation: angle, delayDegrees: 0))

                Moon(color: color)
                    .frame(width: animationSize, height: animationSize)
                    .position(x: size.width / 2, y: size.height / 2)
                    .rotationEffect(Angle.degrees(angle + 130))
                    .modifier(FadeWithRotation(rotation: angle, delayDegrees: 180))
            }
            .frame(width: size.width, height: size.height, alignment: .topLeading)
            .scaleEffect(x: geometry.size.width / size.width, y: geometry.size.height / size.height)
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .aspectRatio(size, contentMode: .fit)
        .onAppear {
            withAnimation(.linear(duration: rotationDuration).repeatForever(autoreverses: false)) {
                angle = 360
            }
        }
    }
}

struct FadeWithRotation: AnimatableModifier {
    // The rotation angle applied to the shape (in degrees)
    var rotation: Double
    // A delay (in degrees) to shift the fade window relative to the rotation.
    // For example, if you want to delay the fade by a quarter-cycle (i.e. 90°),
    // set delayDegrees to 90.
    var delayDegrees: Double = 0

    // Compute the effective angle by subtracting the delay.
    private var effectiveAngle: Double {
        var angle = (rotation - delayDegrees).truncatingRemainder(dividingBy: 360)
        if angle < 0 { angle += 360 }
        return angle
    }

    // The fade function returns a triangle-wave opacity value for an angle in [0, 180]:
    //  • For 0° → 90°: opacity rises linearly from 0 to 1.
    //  • For 90° → 180°: opacity falls linearly from 1 to 0.
    //  • For angles >= 180°: opacity is 0.
    private var computedOpacity: Double {
        if effectiveAngle < 180 {
            let fraction = effectiveAngle / 180.0
            return fraction <= 0.5 ? fraction * 2 : 2 * (1 - fraction)
        } else {
            return 0
        }
    }

    // Make the modifier animatable.
    var animatableData: Double {
        get { rotation }
        set { rotation = newValue }
    }

    func body(content: Content) -> some View {
        content.opacity(computedOpacity)
    }
}

#Preview {
    ImageLoaderShape(color: .red)
        .frame(width: 300)

    ImageLoaderShape(color: .blue)
        .frame(width: 300, height: 100)
}
