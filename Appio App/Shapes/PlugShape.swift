//
//  PlugShape.swift
//  Appio
//
//  Created by gondo on 30/10/2024.
//
//  Converted via desktop app SVGShaper.app
//

import SwiftUI

struct PlugShape: View {
    private let size = CGSize(width: 1424, height: 494)
    private var color: Color

    init(color: Color) {
        self.color = color
    }

    struct LeftPLug: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 609, y: 120))
                path.addCurve(to: CGPoint(x: 613.844, y: 322.668),
                              control1: CGPoint(x: 618.849, y: 151.959),
                              control2: CGPoint(x: 622.761, y: 298.525))
                path.addCurve(to: CGPoint(x: 417.496, y: 290.573),
                              control1: CGPoint(x: 582.609, y: 407.245),
                              control2: CGPoint(x: 444.29, y: 354.047))
                path.addCurve(to: CGPoint(x: 409.371, y: 117.094),
                              control1: CGPoint(x: 396.375, y: 240.539),
                              control2: CGPoint(x: 384.657, y: 167.734))
                path.addCurve(to: CGPoint(x: 533.892, y: 28.0076),
                              control1: CGPoint(x: 433.394, y: 67.8677),
                              control2: CGPoint(x: 482.205, y: 37.7926))
                path.addCurve(to: CGPoint(x: 601.898, y: 70.5221),
                              control1: CGPoint(x: 568.716, y: 21.4152),
                              control2: CGPoint(x: 597.345, y: 35.0778))
                path.addCurve(to: CGPoint(x: 606.93, y: 109.214),
                              control1: CGPoint(x: 603.573, y: 83.5641),
                              control2: CGPoint(x: 605.876, y: 96.0605))
            }
        }
    }

    struct Stick1: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 618, y: 115.77))
                path.addCurve(to: CGPoint(x: 738.898, y: 106.807),
                              control1: CGPoint(x: 649.351, y: 115.77),
                              control2: CGPoint(x: 694.195, y: 113.918))
            }
        }
    }

    struct Stick2: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 646, y: 256.768))
                path.addCurve(to: CGPoint(x: 743.73, y: 255.444),
                              control1: CGPoint(x: 584.61, y: 261.8),
                              control2: CGPoint(x: 703.868, y: 255.444))
            }
        }
    }

    struct LeftCord: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 389.408, y: 217.428))
                path.addCurve(to: CGPoint(x: 327.988, y: 214.36),
                              control1: CGPoint(x: 409.907, y: 217.428),
                              control2: CGPoint(x: 348.487, y: 214.36))
                path.addCurve(to: CGPoint(x: 231.433, y: 267.054),
                              control1: CGPoint(x: 284.449, y: 214.36),
                              control2: CGPoint(x: 256.055, y: 233.615))
                path.addCurve(to: CGPoint(x: 213.707, y: 393.304),
                              control1: CGPoint(x: 207.984, y: 298.898),
                              control2: CGPoint(x: 220.035, y: 355.958))
                path.addCurve(to: CGPoint(x: 157.727, y: 464.25),
                              control1: CGPoint(x: 208.402, y: 424.609),
                              control2: CGPoint(x: 186.459, y: 450.356))
                path.addCurve(to: CGPoint(x: 87.1447, y: 484.331),
                              control1: CGPoint(x: 136.359, y: 474.584),
                              control2: CGPoint(x: 110.375, y: 480.031))
                path.addCurve(to: CGPoint(x: 8.0284, y: 472.114),
                              control1: CGPoint(x: 59.7038, y: 489.411),
                              control2: CGPoint(x: 34.3221, y: 478.332))
            }
        }
    }

    struct RightPlug: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 828.316, y: 202.843))
                path.addCurve(to: CGPoint(x: 856.733, y: 378.725),
                              control1: CGPoint(x: 828.988, y: 341.984),
                              control2: CGPoint(x: 831.45, y: 363.717))
                path.addCurve(to: CGPoint(x: 951.456, y: 355.239),
                              control1: CGPoint(x: 887.578, y: 397.035),
                              control2: CGPoint(x: 924.547, y: 368.165))
                path.addCurve(to: CGPoint(x: 1025.88, y: 253.184),
                              control1: CGPoint(x: 990.541, y: 336.464),
                              control2: CGPoint(x: 1017.52, y: 295.504))
                path.addCurve(to: CGPoint(x: 1035.96, y: 179.838),
                              control1: CGPoint(x: 1030.85, y: 228.057),
                              control2: CGPoint(x: 1035.96, y: 205.666))
                path.addCurve(to: CGPoint(x: 1028.69, y: 118.068),
                              control1: CGPoint(x: 1035.96, y: 158.934),
                              control2: CGPoint(x: 1031.09, y: 138.726))
                path.addCurve(to: CGPoint(x: 1008.86, y: 62.0389),
                              control1: CGPoint(x: 1026.19, y: 96.6504),
                              control2: CGPoint(x: 1025.23, y: 78.0119))
                path.addCurve(to: CGPoint(x: 947.002, y: 15.7983),
                              control1: CGPoint(x: 991.805, y: 45.3948),
                              control2: CGPoint(x: 970.746, y: 21.0937))
                path.addCurve(to: CGPoint(x: 842.088, y: 31.1071),
                              control1: CGPoint(x: 916.316, y: 8.9547),
                              control2: CGPoint(x: 860.862, y: -3.1948))
                path.addCurve(to: CGPoint(x: 827.445, y: 202),
                              control1: CGPoint(x: 828.242, y: 56.4043),
                              control2: CGPoint(x: 828.413, y: 88.1893))
            }
        }
    }

    struct RightCord: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 1035.96, y: 176.797))
                path.addCurve(to: CGPoint(x: 1154.53, y: 186),
                              control1: CGPoint(x: 1075.67, y: 176.797),
                              control2: CGPoint(x: 1115.37, y: 176.629))
                path.addCurve(to: CGPoint(x: 1249.32, y: 276.095),
                              control1: CGPoint(x: 1183.21, y: 192.864),
                              control2: CGPoint(x: 1285.44, y: 228.175))
                path.addCurve(to: CGPoint(x: 1197.68, y: 293.359),
                              control1: CGPoint(x: 1239.02, y: 289.764),
                              control2: CGPoint(x: 1214.32, y: 304.211))
                path.addCurve(to: CGPoint(x: 1198.98, y: 254.146),
                              control1: CGPoint(x: 1181.29, y: 282.668),
                              control2: CGPoint(x: 1179.36, y: 260.627))
                path.addCurve(to: CGPoint(x: 1317.68, y: 279.594),
                              control1: CGPoint(x: 1239.44, y: 240.788),
                              control2: CGPoint(x: 1285.71, y: 251.661))
                path.addCurve(to: CGPoint(x: 1382.9, y: 362.09),
                              control1: CGPoint(x: 1343.29, y: 301.976),
                              control2: CGPoint(x: 1361.33, y: 335.713))
                path.addCurve(to: CGPoint(x: 1415.83, y: 416.865),
                              control1: CGPoint(x: 1396.17, y: 378.305),
                              control2: CGPoint(x: 1411.69, y: 395.852))
            }
        }
    }

    struct PathShape7: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 409.43, y: 133.274))
                path.addCurve(to: CGPoint(x: 436.587, y: 110.534),
                              control1: CGPoint(x: 417.38, y: 133.274),
                              control2: CGPoint(x: 430.877, y: 115.262))
            }
        }
    }

    struct PathShape8: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 397.363, y: 165.681))
                path.addCurve(to: CGPoint(x: 404.298, y: 157.662),
                              control1: CGPoint(x: 397.363, y: 162.238),
                              control2: CGPoint(x: 401.997, y: 159.686))
                path.addCurve(to: CGPoint(x: 439.2, y: 127.642),
                              control1: CGPoint(x: 415.824, y: 147.53),
                              control2: CGPoint(x: 427.242, y: 137.264))
            }
        }
    }

    struct PathShape9: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 400.563, y: 192.246))
                path.addCurve(to: CGPoint(x: 437.967, y: 154.375),
                              control1: CGPoint(x: 385.351, y: 201.379),
                              control2: CGPoint(x: 424.933, y: 166.413))
            }
        }
    }

    struct PathShape10: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 402.53, y: 219.147))
                path.addCurve(to: CGPoint(x: 458.284, y: 170.725),
                              control1: CGPoint(x: 414.84, y: 200.005),
                              control2: CGPoint(x: 440.828, y: 183.987))
            }
        }
    }

    struct PathShape11: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 417.357, y: 240.542))
                path.addCurve(to: CGPoint(x: 419.249, y: 243.681),
                              control1: CGPoint(x: 417.714, y: 242.703),
                              control2: CGPoint(x: 416.998, y: 246.21))
                path.addCurve(to: CGPoint(x: 454.79, y: 211.834),
                              control1: CGPoint(x: 429.818, y: 231.802),
                              control2: CGPoint(x: 442.505, y: 221.849))
            }
        }
    }

    struct PathShape12: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 415.067, y: 279.044))
                path.addCurve(to: CGPoint(x: 425.972, y: 269.041),
                              control1: CGPoint(x: 419.761, y: 277.862),
                              control2: CGPoint(x: 422.957, y: 272.495))
                path.addCurve(to: CGPoint(x: 466.534, y: 228.016),
                              control1: CGPoint(x: 438.603, y: 254.571),
                              control2: CGPoint(x: 452.889, y: 241.502))
            }
        }
    }

    struct PathShape13: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 436.441, y: 293.714))
                path.addCurve(to: CGPoint(x: 465.918, y: 268.956),
                              control1: CGPoint(x: 437.562, y: 296.579),
                              control2: CGPoint(x: 464.545, y: 270.304))
            }
        }
    }

    struct PathShape14: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 454.79, y: 321.792))
                path.addCurve(to: CGPoint(x: 485.618, y: 292.159),
                              control1: CGPoint(x: 462.312, y: 309.717),
                              control2: CGPoint(x: 474.948, y: 301.213))
            }
        }
    }

    struct PathShape15: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 474.491, y: 335.873))
                path.addCurve(to: CGPoint(x: 490.668, y: 319.522),
                              control1: CGPoint(x: 478.983, y: 329.36),
                              control2: CGPoint(x: 484.568, y: 324.599))
            }
        }
    }

    struct PathShape16: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 840.737, y: 63.7899))
                path.addCurve(to: CGPoint(x: 839.504, y: 64.2265),
                              control1: CGPoint(x: 838.59, y: 66.9895),
                              control2: CGPoint(x: 834.641, y: 67.9159))
                path.addCurve(to: CGPoint(x: 873.737, y: 42.1008),
                              control1: CGPoint(x: 850.303, y: 56.0325),
                              control2: CGPoint(x: 862.225, y: 49.22))
            }
        }
    }

    struct PathShape17: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 827.907, y: 102.754))
                path.addCurve(to: CGPoint(x: 833.234, y: 100.517),
                              control1: CGPoint(x: 828.114, y: 104.829),
                              control2: CGPoint(x: 833.105, y: 100.614))
                path.addCurve(to: CGPoint(x: 877.554, y: 67.1525),
                              control1: CGPoint(x: 848.026, y: 89.4203),
                              control2: CGPoint(x: 862.779, y: 78.2731))
            }
        }
    }

    struct PathShape18: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 828.817, y: 141.257))
                path.addCurve(to: CGPoint(x: 834.8, y: 135.195),
                              control1: CGPoint(x: 828.817, y: 138.316),
                              control2: CGPoint(x: 832.786, y: 136.545))
                path.addCurve(to: CGPoint(x: 879.991, y: 105.529),
                              control1: CGPoint(x: 849.766, y: 125.155),
                              control2: CGPoint(x: 865.058, y: 115.629))
            }
        }
    }

    struct PathShape19: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 830.813, y: 174.883))
                path.addCurve(to: CGPoint(x: 838.052, y: 167.635),
                              control1: CGPoint(x: 832.123, y: 174.55),
                              control2: CGPoint(x: 836.81, y: 168.67))
                path.addCurve(to: CGPoint(x: 882.897, y: 131.001),
                              control1: CGPoint(x: 852.873, y: 155.281),
                              control2: CGPoint(x: 868.015, y: 143.289))
            }
        }
    }

    struct PathShape20: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 828.523, y: 209.855))
                path.addCurve(to: CGPoint(x: 837.998, y: 203.015),
                              control1: CGPoint(x: 831.278, y: 209.739),
                              control2: CGPoint(x: 835.908, y: 204.476))
                path.addCurve(to: CGPoint(x: 880.138, y: 176.102),
                              control1: CGPoint(x: 851.655, y: 193.468),
                              control2: CGPoint(x: 866.096, y: 185.061))
            }
        }
    }

    struct PathShape21: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 825.763, y: 241.926))
                path.addCurve(to: CGPoint(x: 832.014, y: 235.864),
                              control1: CGPoint(x: 826.602, y: 241.926),
                              control2: CGPoint(x: 831.16, y: 236.5))
                path.addCurve(to: CGPoint(x: 880.901, y: 202.961),
                              control1: CGPoint(x: 847.735, y: 224.144),
                              control2: CGPoint(x: 864.62, y: 213.874))
            }
        }
    }

    struct PathShape22: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 829.903, y: 279.671))
                path.addCurve(to: CGPoint(x: 837.833, y: 273.348),
                              control1: CGPoint(x: 832.979, y: 279.671),
                              control2: CGPoint(x: 835.707, y: 275.241))
                path.addCurve(to: CGPoint(x: 887.771, y: 235.663),
                              control1: CGPoint(x: 853.365, y: 259.515),
                              control2: CGPoint(x: 871.026, y: 247.948))
            }
        }
    }

    struct PathShape23: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 825, y: 311.28))
                path.addCurve(to: CGPoint(x: 888.094, y: 267.44),
                              control1: CGPoint(x: 829.208, y: 318.942),
                              control2: CGPoint(x: 883.207, y: 271.425))
            }
        }
    }

    struct PathShape24: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 840.443, y: 343.688))
                path.addCurve(to: CGPoint(x: 854.582, y: 330.695),
                              control1: CGPoint(x: 843.645, y: 337.788),
                              control2: CGPoint(x: 849.229, y: 334.505))
                path.addCurve(to: CGPoint(x: 901.365, y: 293.248),
                              control1: CGPoint(x: 870.872, y: 319.099),
                              control2: CGPoint(x: 886.021, y: 306.041))
            }
        }
    }

    struct PathShape25: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 854.184, y: 366.764))
                path.addCurve(to: CGPoint(x: 863.584, y: 360.937),
                              control1: CGPoint(x: 857.579, y: 367.979),
                              control2: CGPoint(x: 861.524, y: 362.72))
                path.addCurve(to: CGPoint(x: 918.628, y: 316.198),
                              control1: CGPoint(x: 881.484, y: 345.442),
                              control2: CGPoint(x: 900.025, y: 330.836))
            }
        }
    }

    struct PathShape26: Shape {
        func path(in _: CGRect) -> Path {
            Path { path in
                path.move(to: CGPoint(x: 899.075, y: 366.133))
                path.addCurve(to: CGPoint(x: 904.441, y: 362.336),
                              control1: CGPoint(x: 899.047, y: 366.137),
                              control2: CGPoint(x: 903.395, y: 363.099))
                path.addCurve(to: CGPoint(x: 937.859, y: 336.332),
                              control1: CGPoint(x: 915.849, y: 354.013),
                              control2: CGPoint(x: 926.884, y: 345.213))
            }
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                LeftPLug()
                    .stroke(color, style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                Stick1()
                    .stroke(color, style: StrokeStyle(lineWidth: 32, lineCap: .round, lineJoin: .round))
                Stick2()
                    .stroke(color, style: StrokeStyle(lineWidth: 32, lineCap: .round, lineJoin: .round))
                LeftCord()
                    .stroke(color, style: StrokeStyle(lineWidth: 16, lineCap: .round, lineJoin: .round))
                RightPlug()
                    .stroke(color, style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                RightCord()
                    .stroke(color, style: StrokeStyle(lineWidth: 16, lineCap: .round, lineJoin: .round))
                PathShape7()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape8()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape9()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape10()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape11()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape12()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape13()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape14()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape15()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape16()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape17()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape18()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape19()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape20()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape21()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape22()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape23()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape24()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape25()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
                PathShape26()
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round, lineJoin: .round))
            }
            .frame(width: size.width, height: size.height, alignment: .topLeading)
            .scaleEffect(x: geometry.size.width / size.width, y: geometry.size.height / size.height)
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .aspectRatio(size, contentMode: .fit)
    }
}

#Preview {
    PlugShape(color: .red)
}
