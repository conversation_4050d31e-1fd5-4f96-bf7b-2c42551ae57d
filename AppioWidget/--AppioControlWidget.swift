////
////  AppioControlWidget.swift
////  AppioWidget
////
////  Created by gondo on 04/04/2025.
////
//
/// **
//    Connect ControlWidget to load Service and generate toggle based on config. Clicking should launch app
//
//    // call from app to reload ControlWidget
//    ControlCenter.shared.reloadAllControls()    // all
//    ControlCenter.shared.reloadControls(ofKind: AppioControlWidget.kind)    // specific one
//
//    read ControlPushHandler  https://developer.apple.com/documentation/widgetkit/controlpushhandler/
//
//
// https://developer.apple.com/videos/play/wwdc2024/10157/
// https://www.rudrank.com/exploring-widgetkit-configurable-control-widgets-ios-18-swiftui/
// https://stackoverflow.com/questions/78716058/ios-18-control-widget-that-opens-a-url
// https://github.com/tomhamming/ControlCenterOpenApp/blob/main/ControlWidgets/ControlWidgetsControl.swift
// https://github.com/onmyway133/blog/issues/983
// */
//
// import AppIntents
// import SwiftUI
// import WidgetKit
//
// @available(iOS 18.0, *)
// struct AppioControlWidget: ControlWidget {
//    static let kind: String = "so.appio.app.AppioControlWidget"
//
//    var body: some ControlWidgetConfiguration {
//        AppIntentControlConfiguration(
//            kind: Self.kind,
//            provider: Provider()
//        ) { value in
//
////            ControlWidgetButton(action: AppioButtonAction(serviceId: value.serviceId)) {
//            ControlWidgetButton(action: LaunchAppIntent(serviceId: value.serviceId)) {
//                Label(value.isOn ? "Is On" : "Is Off", systemImage: value.isOn ? "lightswitch.on" : "lightswitch.off")
////                    .controlWidgetActionHint(value.isOn ? "On" : "Off") // when holding action button: "Hold to ..." -> "Hold to On"
//            }
//            .tint(value.tint)
//
//            // OS maintains state, can lead to inconsistences if API sync fails
////            ControlWidgetToggle(
////                value.label,
////                isOn: value.isOn,
////                action: AppioToggleAction(serviceId: value.serviceId)
////            ) { isOn in
////                Label(isOn ? "Is On" : "Is Off", systemImage: isOn ? "lightswitch.on" : "lightswitch.off")
////                    .controlWidgetActionHint(isOn ? "On" : "Off") // when holding action button: "Hold to ..." -> "Hold to On"
//////                    .controlWidgetStatus("test") // appears in Control Center when state changes
////            }
////            .tint(value.tint)
//        }
//        .displayName("Button") // displayed in gallery when adding ControlWidget to Control Center
//        .description("Select Service") // displayed on ControlWidget configuration
//        .promptsForUserConfiguration() // prompts user to configure ControlWidget
//    }
// }
//
// @available(iOS 18.0, *)
// extension AppioControlWidget {
//    struct Value {
//        var label: String
//        var isOn: Bool
//        var serviceId: String
//        var tint: Color?
//    }
//
//    struct Provider: AppIntentControlValueProvider {
//        func previewValue(configuration: ButtonConfiguration) -> Value {
//            AppioControlWidget.Value(label: "Service Preview", isOn: true, serviceId: configuration.serviceId)
//        }
//
//        func currentValue(configuration: ButtonConfiguration) async throws -> Value {
//            print("Current value: \(configuration.serviceId)")
//
////            // TO+DO: sync state, don't use UserDefaults!
////            let onValue = UserDefaults(suiteName: "group.so.appio.app")?.bool(forKey: "appio-toogle") ?? false
////            print("ison: \(onValue)")
//
//            let isOn = Bool.random()
//            return AppioControlWidget.Value(label: "Service Label", isOn: isOn, serviceId: configuration.serviceId, tint: .yellow)
//        }
//    }
// }
//
// @available(iOS 18.0, *)
// struct ButtonConfiguration: ControlConfigurationIntent {
//    static let title: LocalizedStringResource = "Appio Button Title"
//
//    @Parameter(title: "Service", default: "Choose")
//    var serviceId: String // TO+DO: change to ServiceType
// }
//
// @available(iOS 18.0, *)
// struct LaunchAppIntent: AppIntent {
//    static var title: LocalizedStringResource = "Launch App"
//
//    static var openAppWhenRun = true
//    static var isDiscoverable = true
//
//    @Parameter(title: "Service")
//    var serviceId: String
//
//    init() {}
//    init(serviceId: String) {
//        self.serviceId = serviceId
//    }
//
//    @MainActor
//    func perform() async throws -> some IntentResult & OpensIntent {
//        print("Launch App with service: \(serviceId)")
//        return .result(opensIntent: OpenURLIntent(URL(string: "appio://appio/control-widget/123/open")!)) // TO+DO: proper widget id + handle opening in main app
//    }
// }
//
////@available(iOS 18.0, *)
////enum LaunchAppEnum: String, AppEnum {
////    case home
////
////    static var typeDisplayRepresentation = TypeDisplayRepresentation("Launch Home")
////    static var caseDisplayRepresentations = [
////        LaunchAppEnum.home : DisplayRepresentation("Home"),
////    ]
////}
//
////@available(iOS 18.0, *)
////struct AppioButtonAction: AppIntent {
////    static let title: LocalizedStringResource = "Button Action"
////
////    @Parameter(title: "Service")
////    var serviceId: String
////
////    init() {}
////    init(serviceId: String) {
////        self.serviceId = serviceId
////    }
////
////    // durig this time, the control will be lit
////    @MainActor
////    func perform() async throws -> some IntentResult {
////        print("Perform for: \(serviceId)")
////
////        // TO+DO: Button action: call API, store response to reflect label on button.
////        let state = Bool.random()
////        print("state: \(state)")
////        // TO+DO: UserDefaults doesn't work somehow. don't use UserDefaults!
////        UserDefaults(suiteName: "group.so.appio.app")?.set(state, forKey: "appio-test")
////        ControlCenter.shared.reloadControls(ofKind: AppioControlWidget.kind) // call to re-render widget. shouldn't be ControlCenter?
////
//////        try await Task.sleep(for: .seconds(3))
////
////        return .result()
////    }
////}
