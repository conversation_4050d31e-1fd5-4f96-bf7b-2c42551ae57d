//
//  RefreshWidgetIntent.swift
//  Appio
//
//  Created by gondo on 09/04/2025.
//

import AppIntents
import WidgetKit

struct RefreshWidgetIntent: AppIntent {
    static var title: LocalizedStringResource = "Refresh Widget"

    func perform() async throws -> some IntentResult {
        print("Refresh Intent")

        WidgetCenter.shared.reloadTimelines(ofKind: WidgetShared.intentKind)
        return .result()
    }
}
