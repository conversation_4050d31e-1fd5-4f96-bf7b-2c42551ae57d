//
//  WidgetFamilyExtension.swift
//  Appio
//
//  Created by gondo on 16/09/2025.
//

import WidgetKit

extension WidgetFamily {
    var humanDescription: String {
        switch self {
        case .systemSmall:
            return "Small"
        case .systemMedium:
            return "Medium"
        case .systemLarge:
            return "Large"
        case .systemExtraLarge:
            return "Extra Large"
        case .accessoryCircular, .accessoryRectangular, .accessoryInline:
            return "Widget"
        @unknown default:
            return "Widget"
        }
    }
}
