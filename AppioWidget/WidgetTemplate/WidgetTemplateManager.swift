import Foundation

enum TemplateError: Error {
    case invalidJSON
}

class WidgetTemplateManager {
    static let shared = WidgetTemplateManager()

    private init() {}

    func loadTemplateConfig(serviceId: String, widgetId: String) async throws -> WidgetTemplate? {
        Log.shared.warning("Fetching widget: \(widgetId)")

        var widget: WidgetEntity
        do {
            widget = try await WidgetsManager.fetchWidget(sericeId: serviceId, widgetId: widgetId)
        } catch {
            guard let storedWidget = WidgetsManager.get(serviceId: serviceId, id: widgetId) else {
                return nil
            }
            Log.shared.warning("Using stored WidgetEntity")
            widget = storedWidget
        }

        guard let data = widget.config.data(using: .utf8) else {
            throw TemplateError.invalidJSON
        }

        do {
            Log.shared.warning("Decoding widget to WidgetTemplate")
            return try JSONDecoder().decode(WidgetTemplate.self, from: data)
        } catch let error as DecodingError {
            switch error {
            case let .dataCorrupted(context):
                print("Data corrupted:", context.debugDescription)
            case let .keyNotFound(key, context):
                print("Key '\(key)' not found:", context.debugDescription)
            case let .typeMismatch(type, context):
                print("Type mismatch for type \(type):", context.debugDescription)
            case let .valueNotFound(value, context):
                print("Value '\(value)' not found:", context.debugDescription)
            @unknown default:
                print("unkown")
            }
            throw error
        } catch {
            throw TemplateError.invalidJSON
        }
    }
}
