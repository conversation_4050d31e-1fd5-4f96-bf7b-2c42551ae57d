//
//  WidgetTemplateBuilder.swift
//  Appio
//
//  Created by gondo on 07/04/2025.
//

import SwiftUI
import WidgetKit

@MainActor
enum WidgetTemplateBuilder {
    static func buildRootView(entry: AppioWidgetEntry, from variant: WidgetVariant, colorScheme: ColorScheme, renderingMode: WidgetRenderingMode) -> AnyView {
        AnyView(
            VStack(spacing: 0) {
                ForEach(variant.elements ?? []) { child in
                    buildView(entry: entry, from: child, colorScheme: colorScheme, renderingMode: renderingMode)
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .if(variant.properties.background != nil) { view in
                if #available(iOS 26.0, *), renderingMode != .fullColor {
                    view.background(.clear)
                } else {
                    view.background(variant.properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                }
            }
            .widgetURL(entry.linkToAppURL(externalUrl: variant.properties.url))
        )
    }

    static func buildView(entry: AppioWidgetEntry, from element: WidgetElement, colorScheme: ColorScheme, renderingMode: WidgetRenderingMode) -> AnyView {
        switch element.type {
        case .refreshButton:
            guard case let .refreshButton(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Button(intent: RefreshWidgetIntent()) {
                    Text(properties.text ?? "Refresh")
                        .if(properties.color != nil) { view in
                            view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                        }
                }
                .if(properties.tint != nil) { view in
                    view.tint(properties.tint!.toColor(colorScheme: colorScheme, fallback: .primary))
                }
                .if(properties.style?.lowercased() == "bordered") { view in
                    view.buttonStyle(.bordered)
                }
                .if(properties.style?.lowercased() == "borderedprominent") { view in
                    if #available(iOS 26.0, *), renderingMode != .fullColor {
                        view.buttonStyle(.bordered)
                    } else {
                        view.buttonStyle(.borderedProminent)
                    }
                }
                .if(properties.style?.lowercased() == "plain") { view in
                    view.buttonStyle(.plain)
                }
                .if(properties.style?.lowercased() == "borderless") { view in
                    view.buttonStyle(.borderless)
                }
                .applyPadding(properties.padding)
            )

        case .hstack:
            guard case let .hstack(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                HStack(alignment: properties.alignment?.toVerticalAlignment ?? .center, spacing: properties.spacing) {
                    ForEach(element.elements ?? []) { child in
                        buildView(entry: entry, from: child, colorScheme: colorScheme, renderingMode: renderingMode)
                    }
                }
                .if(properties.background != nil) { view in
                    if #available(iOS 26.0, *), renderingMode != .fullColor {
                        view.background(.clear)
                    } else {
                        view.background(properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                    }
                }
            )

        case .vstack:
            guard case let .vstack(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                VStack(alignment: properties.alignment?.toHorizontalAlignment ?? .center, spacing: properties.spacing) {
                    ForEach(element.elements ?? []) { child in
                        buildView(entry: entry, from: child, colorScheme: colorScheme, renderingMode: renderingMode)
                    }
                }
                .if(properties.background != nil) { view in
                    if #available(iOS 26.0, *), renderingMode != .fullColor {
                        view.background(.clear)
                    } else {
                        view.background(properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                    }
                }
            )

        case .zstack:
            guard case let .zstack(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                ZStack(alignment: properties.alignment?.toAlignment ?? .center) {
                    ForEach(element.elements ?? []) { child in
                        buildView(entry: entry, from: child, colorScheme: colorScheme, renderingMode: renderingMode)
                    }
                }
                .if(properties.background != nil) { view in
                    if #available(iOS 26.0, *), renderingMode != .fullColor {
                        view.background(.clear)
                    } else {
                        view.background(properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                    }
                }
            )

        case .text:
            guard case let .text(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Text(properties.text)
                    .font(.system(size: properties.fontSize ?? DefaultTextProperties.fontSize))
                    .fontWeight(properties.fontWeight?.toFontWeight)
                    .if(properties.italic == true) { view in
                        view.italic()
                    }
                    .if(properties.strikethrough == true) { view in
                        view.strikethrough()
                    }
                    .if(properties.underline == true) { view in
                        view.underline()
                    }
                    .if(properties.color != nil) { view in
                        view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                    }
                    .if(properties.background != nil) { view in
                        if #available(iOS 26.0, *), renderingMode != .fullColor {
                            view.background(.clear)
                        } else {
                            view.background(properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                        }
                    }
                    .if(properties.alignment != nil) { view in
                        view.multilineTextAlignment(properties.alignment!.toTextAlignment)
                    }
                    .applyPadding(properties.padding)
            )

        case .image:
            guard case let .image(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Group {
                    if let imageUrl = URL(string: properties.src ?? "") {
                        Image.cached(url: imageUrl)
                            .resizable()
                            .if(renderingMode == .accented) { img in
                                if #available(iOS 18.0, *) {
                                    img.widgetAccentedRenderingMode(.fullColor)
                                } else {
                                    img
                                }
                            }
                            .if(properties.contentMode != nil || properties.width != nil || properties.height != nil) { view in
                                view
                                    .aspectRatio(contentMode: properties.contentMode?.toContentMode ?? .fit)
                            }
                            .if(properties.width != nil || properties.height != nil) { view in
                                view
                                    .frame(width: properties.width, height: properties.height)   
                            }
                            .if(properties.cornerRadius != nil || properties.borderColor != nil || properties.borderWidth != nil) { view in
                                view
                                    .overlay(
                                        RoundedRectangle(cornerRadius: properties.cornerRadius ?? 0)
                                            .stroke(
                                                properties.borderColor?.toColor(colorScheme: colorScheme, fallback: .clear) ?? .clear,
                                                lineWidth: properties.borderWidth ?? 0
                                            )
                                    )
                                    .clipShape(RoundedRectangle(cornerRadius: properties.cornerRadius ?? 0))
                            }
                    } else if let symbol = properties.symbol {
                        Image(systemName: symbol)
                    } else {
                        Image.placeholder()
                            .if(renderingMode == .accented) { view in
                                if #available(iOS 18, *) {
                                    view.widgetAccentedRenderingMode(.fullColor)
                                } else {
                                    view
                                }
                            }
                    }
                }
                .applyPadding(properties.padding)
                .opacity(properties.opacity ?? 1)
            )

        case .spacer:
            guard case let .spacer(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Spacer()
                    .applyPadding(properties.padding)
            )

        case .rectangle:
            guard case let .rectangle(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                RoundedRectangle(cornerSize: CGSize(width: properties.cornerRadius ?? 0, height: properties.cornerRadius ?? 0))
                    .if(properties.width != nil || properties.height != nil) { view in
                        view
                            .frame(width: properties.width, height: properties.height)
                    }
                    .if(properties.color != nil) { view in
                        view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .clear))
                    }
                    .applyPadding(properties.padding)
                    .opacity(properties.opacity ?? 1)
            )

        case .ellipse:
            guard case let .ellipse(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Ellipse()
                    .if(properties.width != nil || properties.height != nil) { view in
                        view
                            .frame(width: properties.width, height: properties.height)
                    }
                    .if(properties.color != nil) { view in
                        view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .clear))
                    }
                    .applyPadding(properties.padding)
                    .opacity(properties.opacity ?? 1)
            )

        case .gauge:
            guard case let .gauge(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Gauge(
                    value: properties.value,
                    in: (properties.min ?? 0) ... (properties.max ?? 100),
                ) {
                    if let text = properties.label {
                        Text(text)
                            .if(properties.color != nil) { view in
                                view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                            }
                    } else {
                        EmptyView()
                    }
                } currentValueLabel: {
                    if let text = properties.currentValueLabel {
                        Text(text)
                            .if(properties.color != nil) { view in
                                view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                            }
                    } else {
                        EmptyView()
                    }
                } minimumValueLabel: {
                    if let text = properties.minimumValueLabel {
                        Text(text)
                            .if(properties.color != nil) { view in
                                view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                            }
                    } else {
                        EmptyView()
                    }
                } maximumValueLabel: {
                    if let text = properties.maximumValueLabel {
                        Text(text)
                            .if(properties.color != nil) { view in
                                view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                            }
                    } else {
                        EmptyView()
                    }
                }
                .gaugeStyle(properties.style?.toGaugeStyle ?? AnyGaugeStyle(.automatic))
                .scaleEffect(properties.scaleEffect ?? 1)
                .if(properties.tint != nil) { view in
                    view.tint(properties.tint!.toColor(colorScheme: colorScheme, fallback: .primary))
                }
                .applyPadding(properties.padding)
                .opacity(properties.opacity ?? 1)
            )

        case .lastUpdated:
            guard case let .lastUpdated(properties) = element.properties else { return AnyView(EmptyView()) }
            return AnyView(
                Text(entry.date.humanReadableTime)
                    .font(.system(size: properties.fontSize ?? DefaultTextProperties.fontSize))
                    .if(properties.color != nil) { view in
                        view.foregroundStyle(properties.color!.toColor(colorScheme: colorScheme, fallback: .primary))
                    }
                    .if(properties.background != nil) { view in
                        if #available(iOS 26.0, *), renderingMode != .fullColor {
                            view.background(.clear)
                        } else {
                            view.background(properties.background!.toColor(colorScheme: colorScheme, fallback: .clear))
                        }
                    }
                    .applyPadding(properties.padding)
            )
        }
    }
}
