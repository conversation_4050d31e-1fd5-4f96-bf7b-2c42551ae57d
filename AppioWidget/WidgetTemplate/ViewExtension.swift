//
//  ViewExtension.swift
//  Appio
//
//  Created by gondo on 10/04/2025.
//

import SwiftUI
import WidgetKit

extension View {
    func applyPadding(_ padding: Padding?) -> some View {
        self
            .padding(.top, padding?.top ?? 0)
            .padding(.bottom, padding?.bottom ?? 0)
            .padding(.leading, padding?.left ?? 0)
            .padding(.trailing, padding?.right ?? 0)
    }
    
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, @ViewBuilder transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    func apply<V: View>(@ViewBuilder _ block: (Self) -> V) -> V { block(self) }
}
