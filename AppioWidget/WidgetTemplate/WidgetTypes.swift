//
//  WidgetTypes.swift
//  Appio
//
//  Created by gondo on 07/04/2025.
//

import SwiftUI
import WidgetKit

// Represents different types of widget elements
enum WidgetElementType: String, Codable {
    case refreshButton
    case hstack
    case vstack
    case zstack
    case text
    case image
    case spacer
    case rectangle
    case ellipse
    case gauge
    case lastUpdated
}

// MARK: - type properties

struct RefreshButtonProperties: Codable {
    let text: String?
    let style: String?
    let color: String?
    let tint: String?
    let padding: Padding?
}

struct StackProperties: Codable {
    let alignment: String?
    let spacing: CGFloat?
    let background: String?
}

struct TextProperties: Codable {
    let text: String // required

    let fontSize: CGFloat?
    let fontWeight: String?
    let italic: Bool?
    let strikethrough: Bool?
    let underline: Bool?

    let color: String?
    let background: String?

    let alignment: String?
    let padding: Padding?
}

enum DefaultTextProperties {
    static let fontSize: CGFloat = 16
}

struct ImageProperties: Codable {
    let src: String? // not using `url` so we can parse and pre-cache all links. otherwise conflict with other link urls
    let symbol: String? // SF symbol

    let contentMode: String?
    let width: CGFloat?
    let height: CGFloat?

    let opacity: Double?
    let cornerRadius: CGFloat?
    let borderWidth: CGFloat?
    let borderColor: String?

    let padding: Padding?
}

struct SpacerProperties: Codable {
    let padding: Padding?
    let length: CGFloat? // minLength
}

struct RectangleProperties: Codable {
    let cornerRadius: Double?
    let color: String?
    let width: CGFloat?
    let height: CGFloat?
    let padding: Padding?
    let opacity: Double?
}

struct EllipseProperties: Codable {
    let color: String?
    let width: CGFloat?
    let height: CGFloat?
    let padding: Padding?
    let opacity: Double?
}

struct GaugeProperties: Codable {
    let value: Double // within min...max
    let min: Double? // default: 0
    let max: Double? // default: 100
    let label: String?
    let currentValueLabel: String?
    let minimumValueLabel: String?
    let maximumValueLabel: String?
    let style: String?
    let padding: Padding?
    let opacity: Double?
    let color: String? // text
    let tint: String? // shape
    let scaleEffect: CGFloat?
}

struct LastUpdatedProperties: Codable {
    let fontSize: CGFloat?
    let color: String?
    let background: String?
    let padding: Padding?
}

struct WidgetVariantProperties: Codable {
    let version: String?
    let supportedFamilies: [String]?
    let background: String?
    let url: String?
//    let padding: Padding?
}

// MARK: - generic properties

struct Padding: Codable {
    let top: CGFloat?
    let bottom: CGFloat?
    let left: CGFloat?
    let right: CGFloat?
}

// MARK: - Properties enum to hold type-specific properties

enum ElementProperties {
    case refreshButton(RefreshButtonProperties)
    case hstack(StackProperties)
    case vstack(StackProperties)
    case zstack(StackProperties)
    case text(TextProperties)
    case image(ImageProperties)
    case spacer(SpacerProperties)
    case rectangle(RectangleProperties)
    case ellipse(EllipseProperties)
    case gauge(GaugeProperties)
    case lastUpdated(LastUpdatedProperties)
}

// MARK: - Main template structure

struct WidgetTemplate: Codable {
    let variants: [WidgetVariant]?

    func pick(by family: WidgetFamily) -> WidgetVariant? { // WidgetElement
        Log.shared.warning("Matching family: \(family.description.lowercased()) to families:")

        return variants?.first {
            guard let families = $0.properties.supportedFamilies else { return false }
            Log.shared.warning("Families: \(families.joined(separator: ", "))")
            return families.contains { $0.lowercased() == family.description.lowercased() }
        }
    }
}

struct WidgetVariant: Codable {
    let properties: WidgetVariantProperties
    let elements: [WidgetElement]?
}

struct WidgetElement: Codable, Identifiable {
    let id: UUID = .init() // auto-generated every time
    let type: WidgetElementType
    let properties: ElementProperties
    let elements: [WidgetElement]?

    private enum CodingKeys: String, CodingKey {
        case type, properties, elements
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        type = try container.decode(WidgetElementType.self, forKey: .type)
        elements = try container.decodeIfPresent([WidgetElement].self, forKey: .elements)

        switch type {
        case .refreshButton: properties = try .refreshButton(container.decode(RefreshButtonProperties.self, forKey: .properties))
        case .hstack: properties = try .hstack(container.decode(StackProperties.self, forKey: .properties))
        case .vstack: properties = try .vstack(container.decode(StackProperties.self, forKey: .properties))
        case .zstack: properties = try .zstack(container.decode(StackProperties.self, forKey: .properties))
        case .text: properties = try .text(container.decode(TextProperties.self, forKey: .properties))
        case .image: properties = try .image(container.decode(ImageProperties.self, forKey: .properties))
        case .spacer: properties = try .spacer(container.decode(SpacerProperties.self, forKey: .properties))
        case .rectangle: properties = try .rectangle(container.decode(RectangleProperties.self, forKey: .properties))
        case .ellipse: properties = try .ellipse(container.decode(EllipseProperties.self, forKey: .properties))
        case .gauge: properties = try .gauge(container.decode(GaugeProperties.self, forKey: .properties))
        case .lastUpdated: properties = try .lastUpdated(container.decode(LastUpdatedProperties.self, forKey: .properties))
        }
    }

    func encode(to encoder: Encoder) throws {
        print("Encoding")

        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(type, forKey: .type)
        try container.encodeIfPresent(elements, forKey: .elements)

        switch properties {
        case let .refreshButton(props): try container.encode(props, forKey: .properties)
        case let .hstack(props): try container.encode(props, forKey: .properties)
        case let .vstack(props): try container.encode(props, forKey: .properties)
        case let .zstack(props): try container.encode(props, forKey: .properties)
        case let .text(props): try container.encode(props, forKey: .properties)
        case let .image(props): try container.encode(props, forKey: .properties)
        case let .spacer(props): try container.encode(props, forKey: .properties)
        case let .rectangle(props): try container.encode(props, forKey: .properties)
        case let .ellipse(props): try container.encode(props, forKey: .properties)
        case let .gauge(props): try container.encode(props, forKey: .properties)
        case let .lastUpdated(props): try container.encode(props, forKey: .properties)
        }
    }
}
