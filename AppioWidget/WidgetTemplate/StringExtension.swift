//
//  StringExtension.swift
//  Appio
//
//  Created by gondo on 07/04/2025.
//

import SwiftUI

extension String {
    var toTextAlignment: TextAlignment {
        switch lowercased() {
        case "leading": .leading
        case "trailing": .trailing
        default: .center
        }
    }

    var toVerticalAlignment: VerticalAlignment {
        switch lowercased() {
        case "top": .top
        case "bottom": .bottom
        default: .center
        }
    }

    var toHorizontalAlignment: HorizontalAlignment {
        switch lowercased() {
        case "leading": .leading
        case "trailing": .trailing
        default: .center
        }
    }

    var toAlignment: Alignment {
        switch lowercased() {
        case "topleading": .topLeading
        case "top": .top
        case "toptrailing": .topTrailing
        case "leading": .leading
        case "center": .center
        case "trailing": .trailing
        case "bottomleading": .bottomLeading
        case "bottom": .bottom
        case "bottomtrailing": .bottomTrailing
        default: .center
        }
    }

    // Docs: https://developer.apple.com/documentation/swiftui/font/weight
    var toFontWeight: Font.Weight {
        switch lowercased() {
        case "black": .black
        case "bold": .bold
        case "heavy": .heavy
        case "light": .light
        case "medium": .medium
        case "regular": .regular
        case "semibold": .semibold
        case "thin": .thin
        case "ultralight": .ultraLight
        default: .regular
        }
    }

    var toContentMode: ContentMode {
        switch lowercased() {
        case "fit": .fit
        case "fill": .fill
        default: .fit
        }
    }

    var toGaugeStyle: AnyGaugeStyle {
        switch lowercased() {
        case "linearcapacity": AnyGaugeStyle(.linearCapacity)
        case "accessorycircular": AnyGaugeStyle(.accessoryCircular)
        case "accessorylinear": AnyGaugeStyle(.accessoryLinear)
        case "accessorycircularcapacity": AnyGaugeStyle(.accessoryCircularCapacity)
        case "accessorylinearcapacity": AnyGaugeStyle(.accessoryLinearCapacity)
        default: AnyGaugeStyle(.automatic)
        }
    }

    func toColor(colorScheme: ColorScheme, fallback: Color) -> Color {
        switch lowercased() {
        case "accentColor": return .accentColor
        case "black": return .black
        case "blue": return .blue
        case "brown": return .brown
        case "clear": return .clear
        case "cyan": return .cyan
        case "gray": return .gray
        case "green": return .green
        case "indigo": return .indigo
        case "mint": return .mint
        case "orange": return .orange
        case "pink": return .pink
        case "primary": return .primary
        case "purple": return .purple
        case "red": return .red
        case "secondary": return .secondary
        case "teal": return .teal
        case "white": return .white
        case "yellow": return .yellow
            
        // Matching Android colors
        case "aqua": return colorScheme == .dark ? Color(hex: "#66FFFF")! : Color(hex: "#00FFFF")!
        case "darkgray": return colorScheme == .dark ? Color(hex: "#D3D3D3")! : Color(hex: "#A9A9A9")!
        case "darkgrey": return colorScheme == .dark ? Color(hex: "#D3D3D3")! : Color(hex: "#A9A9A9")!
        case "fuchsia": return colorScheme == .dark ? Color(hex: "#FF66FF")! : Color(hex: "#FF00FF")!
        case "grey": return colorScheme == .dark ? Color(hex: "#B0B0B0")! : Color(hex: "#808080")!
        case "lightgray": return colorScheme == .dark ? Color(hex: "#E8E8E8")! : Color(hex: "#D3D3D3")!
        case "lightgrey": return colorScheme == .dark ? Color(hex: "#E8E8E8")! : Color(hex: "#D3D3D3")!
        case "lime": return colorScheme == .dark ? Color(hex: "#66FF66")! : Color(hex: "#00FF00")!
        case "maroon": return colorScheme == .dark ? Color(hex: "#B00030")! : Color(hex: "#800000")!
        case "navy": return colorScheme == .dark ? Color(hex: "#333399")! : Color(hex: "#000080")!
        case "olive": return colorScheme == .dark ? Color(hex: "#B0B050")! : Color(hex: "#808000")!
        case "silver": return colorScheme == .dark ? Color(hex: "#E0E0E0")! : Color(hex: "#C0C0C0")!
        case "magenta": return colorScheme == .dark ? Color(hex: "#FF66FF")! : Color(hex: "#FF00FF")!
            
        default:
            let colors = split(separator: ",")
            if colors.count == 2 && colorScheme == .dark {
                return Color(hex: String(colors[1])) ?? fallback
            }
            return Color(hex: String(colors[0])) ?? fallback
        }
    }
}

struct AnyGaugeStyle: GaugeStyle {
    private let style: any GaugeStyle

    init(_ style: any GaugeStyle) {
        self.style = style
    }

    func makeBody(configuration: Configuration) -> some View {
        AnyView(style.makeBody(configuration: configuration))
    }
}
