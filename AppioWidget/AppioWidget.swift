//
//  AppioWidget.swift
//  AppioWidget
//
//  Created by gondo on 04/04/2025.
//

import SwiftUI
import WidgetKit

struct Provider: AppIntentTimelineProvider {
    typealias Entry = AppioWidgetEntry

    // when secure information are hidden
    func placeholder(in _: Context) -> AppioWidgetEntry {
        return AppioWidgetEntry(
            isPreview: true, // should be true or false?
            date: Date(),
            configuration: ConfigurationAppIntent(),
            templateConfig: nil,
        )
    }

    // preview gallery (context.isPreview)
    func snapshot(for configuration: ConfigurationAppIntent, in context: Context) async -> AppioWidgetEntry {
        if context.isPreview {
            return AppioWidgetEntry(
                isPreview: true,
                date: Date(),
                configuration: configuration,
                templateConfig: nil,
            )
        }

        let templateConfig = await Self.loadTemplateConfig(for: configuration)
        return AppioWidgetEntry(
            isPreview: false,
            date: Date(),
            configuration: configuration,
            templateConfig: templateConfig,
        )
    }

    func timeline(for configuration: ConfigurationAppIntent, in _: Context) async -> Timeline<AppioWidgetEntry> {
        let templateConfig = await Self.loadTemplateConfig(for: configuration)

        #if DEBUG
            if let tpl = templateConfig { print("Template config:\n\(tpl)") }
        #endif
        
        if let serviceId = configuration.serviceType?.id {
            StorageManager.lastWidgetRefresh[serviceId] = Date()
        }

        return Timeline(
            entries: [
                AppioWidgetEntry(
                    isPreview: false,
                    date: .now,
                    configuration: configuration,
                    templateConfig: templateConfig,
                ),
            ],
            policy: .atEnd
        )
    }
}

extension Provider {
    static func loadTemplateConfig(for configuration: ConfigurationAppIntent) async -> WidgetTemplate? {
        var serviceId = configuration.serviceType?.id
        var widgetId = configuration.widgetType?.id

        // First check if we have a stored auto-selected configuration
        if serviceId == nil || widgetId == nil {
            if let storedConfig = WidgetAutoConfiguration.getStoredConfiguration() {
                Log.shared.warning("Using stored auto-selected configuration: serviceId=\(storedConfig.serviceId), widgetId=\(storedConfig.widgetId)")
                serviceId = storedConfig.serviceId
                widgetId = storedConfig.widgetId
            }
        }

        // Has to use `or` because serviceId is selected via ServiceQuery.DefaultResult() (but not widfetId)
        if serviceId == nil || widgetId == nil {
            let services = ServiceQuery.storedServices()
            if services.count == 1,
               let firstService = services.first
            {
                // auto-select first service
                serviceId = firstService.id

                if let widgets = StorageManager.widgets[firstService.id],
                   widgets.count == 1,
                   let autoWidget = widgets.first
                {
                    // auto-select first widget
                    widgetId = autoWidget.id

                    // Store this auto-selection for future use
                    WidgetAutoConfiguration.storeConfiguration(serviceId: firstService.id, widgetId: autoWidget.id)
                    Log.shared.warning("Stored auto-selected configuration: serviceId=\(firstService.id), widgetId=\(autoWidget.id)")
                }
            }
        }

        guard let configuredServiceId = serviceId,
              let configuredWidgetId = widgetId
        else {
            Log.shared.warning("No Service and Widget selected")
            return nil
        }

        do {
            let templateConfig = try await WidgetTemplateManager.shared.loadTemplateConfig(
                serviceId: configuredServiceId,
                widgetId: configuredWidgetId
            )
            return templateConfig
        } catch {
            Log.shared.error("Failed to load template config")
            return nil
        }
    }
}

// Helper class to manage auto-selected widget configuration
class WidgetAutoConfiguration {
    private static let autoConfigKey = "widget_auto_configuration"

    struct AutoConfiguration: Codable {
        let serviceId: String
        let widgetId: String
        let timestamp: Date
    }

    static func storeConfiguration(serviceId: String, widgetId: String) {
        let config = AutoConfiguration(
            serviceId: serviceId,
            widgetId: widgetId,
            timestamp: Date()
        )

        if let data = try? JSONEncoder().encode(config) {
            StorageManager.userDefaults.set(data, forKey: autoConfigKey)
            Log.shared.warning("Stored widget auto-configuration")
        }
    }

    static func getStoredConfiguration() -> AutoConfiguration? {
        guard let data = StorageManager.userDefaults.data(forKey: autoConfigKey),
              let config = try? JSONDecoder().decode(AutoConfiguration.self, from: data) else {
            return nil
        }

        // Verify the stored configuration is still valid
        let services = ServiceQuery.storedServices()
        guard services.contains(where: { $0.id == config.serviceId }) else {
            // Service no longer exists, clear the stored config
            clearStoredConfiguration()
            return nil
        }

        let widgets = WidgetQuery.storedWidgets()
        guard widgets.contains(where: { $0.id == config.widgetId && $0.serviceId == config.serviceId }) else {
            // Widget no longer exists, clear the stored config
            clearStoredConfiguration()
            return nil
        }

        return config
    }

    static func clearStoredConfiguration() {
        StorageManager.userDefaults.removeObject(forKey: autoConfigKey)
        Log.shared.warning("Cleared widget auto-configuration")
    }
}

struct AppioWidgetEntry: TimelineEntry {
    let isPreview: Bool
    let date: Date
    let configuration: ConfigurationAppIntent
    let templateConfig: WidgetTemplate?

    func linkToAppURL(externalUrl: String? = nil) -> URL {
        guard let widgetType = configuration.widgetType else { return URL(string: "appio://")! }
        var urlString = "appio://appio/widget/?serviceId=\(widgetType.serviceId)&widgetId=\(widgetType.id)"
        if let externalUrl = externalUrl {
            urlString += "&url=\(externalUrl)"
        }
        Log.shared.warning("Widget opening: \(urlString)")
        return URL(string: urlString)!
    }
}

struct AppioWidgetEntryView: View {
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.widgetFamily) var family
    @Environment(\.widgetContentMargins) var margins
    @Environment(\.widgetRenderingMode) var renderingMode

    var entry: Provider.Entry

    var body: some View {
        if entry.isPreview {
            AppioWidgetPreviewView()
        } else if let templateConfig = entry.templateConfig {
            if let variant = templateConfig.pick(by: family) {
                WidgetTemplateBuilder.buildRootView(entry: entry, from: variant, colorScheme: colorScheme, renderingMode: renderingMode)
            } else {
                Text("Widget size not supported…")
                    .multilineTextAlignment(.center)
            }
        } else {
            AppioWidgetEmptyView()
        }
    }
}

struct AppioWidget: Widget {
    var body: some WidgetConfiguration {
        AppIntentConfiguration(
            kind: WidgetShared.intentKind,
            intent: ConfigurationAppIntent.self,
            provider: Provider(),
        ) { entry in
            AppioWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Widget") // displayed when widget ss being added and size is being selected
        .description("Select Widget") // displayed when widget ss being added and size is being
        .contentMarginsDisabled()
        // .supportedFamilies() // has to support all families. family detection happens in AppioWidgetEntryView
        // .pushHandler(AppioWidgetPushHandler.self) // N2H: ios26
    }
}

#if DEBUG
    extension AppioWidgetEntry {
        static func mock() -> AppioWidgetEntry {
            let config = ConfigurationAppIntent()
            config.serviceType = ServiceType(id: "svc_00000000000000000000000000", title: "Your Cool App", logoURL: "https://cdn.appio.so/app/demo.appio.so/logo.png")
            config.widgetType = WidgetType(serviceId: "svc_00000000000000000000000000", id: "wgt_00000000000000000000000000", name: "Preview", templateConfig: "{}")

            let data = WidgetEntity.numberConfig.data(using: .utf8)!
            let templateConfig = try? JSONDecoder().decode(WidgetTemplate.self, from: data)

            return AppioWidgetEntry(
                isPreview: false,
                date: .now,
                configuration: config,
                templateConfig: templateConfig,
            )
        }
        
        static func empty() -> AppioWidgetEntry {
            let emptyConfig = ConfigurationAppIntent()
            
            return AppioWidgetEntry(
                isPreview: false,
                date: .now,
                configuration: emptyConfig,
                templateConfig: nil,
            )
        }
        
        static func preview() -> AppioWidgetEntry {
            let emptyConfig = ConfigurationAppIntent()
            
            return AppioWidgetEntry(
                isPreview: true,
                date: .now,
                configuration: emptyConfig,
                templateConfig: nil,
            )
        }
    }

    #Preview(as: .systemSmall) {
        AppioWidget()
    } timeline: {
        AppioWidgetEntry.mock()
    }

    #Preview("Empty", as: .systemMedium) {
        AppioWidget()
    } timeline: {
        AppioWidgetEntry.empty()
    }

    #Preview("Preview", as: .systemMedium) {
        AppioWidget()
    } timeline: {
        AppioWidgetEntry.preview()
    }
#endif
