opt_out_usage

default_platform(:ios)

platform :ios do
  desc "Build and upload to TestFlight"
  lane :beta do
    build_app(
      scheme: "Appio",
      export_method: "app-store",
      export_options: {
        signingStyle: "automatic",
        compileBitcode: true,
        manageAppVersionAndBuildNumber: true,
        uploadBitcode: true,
        uploadSymbols: true
      }
    )
    upload_to_testflight
  end
  
  desc "Build and upload to App Store"
  lane :release do
    build_app(
      scheme: "Appio",
      export_method: "app-store",
      export_options: {
        signingStyle: "automatic",
        compileBitcode: true,
        manageAppVersionAndBuildNumber: true,
        uploadBitcode: true,
        uploadSymbols: true
      }
    )
    upload_to_app_store(
      # ipa: "build/Appio.ipa",
      clean: true,                   # Re-build app
      skip_screenshots: true,
      skip_metadata: true,
      submit_for_review: true,
      automatic_release: false,
      release_notes: {
        'default' => "Bug fixes and performance improvements."
      }
    )
  end
end
