#!/bin/bash
# File: Scripts/swiftformat.sh
# Make sure the path to swiftformat is correct (installed via Homebrew, Mint, etc.)
SWIFTFORMAT="/usr/local/bin/swiftformat"

# Run swiftformat on the entire project directory.
# $PROJECT_DIR is an environment variable available in Xcode build phases; if unavailable here,
# you can hardcode your project path.
if [ -z "$PROJECT_DIR" ]; then
  echo "PROJECT_DIR not set. Using current directory."
  TARGET_DIR="."
else
  TARGET_DIR="$PROJECT_DIR"
fi

"$SWIFTFORMAT" "$TARGET_DIR"
