#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JWT="$SCRIPT_DIR/../.keys/jwt/jwt"

AUTH_FILE="$SCRIPT_DIR/../.keys/AuthKey_Production_KZK74JKWR5.p8"
AUTH_KEY_ID="KZK74JKWR5"
TEAM_ID="V9P4DCJF8Z"
TOPIC="so.appio.app"
APN_TOKEN="d42ceec7ab05f85ffb8fee4f21043e5cd2883987359560f5202c08ed5f3c52d3"
SERVICE_ID="demo_svc_01jvvt8aqcxq5n24h9h6v62byq"

# ----------------------------------------

echo "🏝️ PRODUCTION"


JWT_TOKEN=$($JWT -team_id=$TEAM_ID -auth_key_id=$AUTH_KEY_ID -auth_key_file=$AUTH_FILE)
echo "JWT TOKEN:"
echo $JWT_TOKEN


DATE=$(date "+%Y-%m-%d %H:%M:%S")
RANDOM_ID=$((RANDOM % 90000 + 10000))  # generates 10000–99999
echo "DATE: $DATE"
echo "Notification ID: ntf_000000000000000000000$RANDOM_ID"


## Foreground with image and sound
## Delivered to watch only if iphone is locked
## On watch the logo takes whole screen
#curl -v \
#-H "apns-topic: $TOPIC" \
#-H "authorization: bearer $JWT_TOKEN" \
#-H "apns-push-type: alert" \
#-d '{
#  "aps": {
#    "alert": {
#      "title": "Title at '"$DATE"'",
#      "subtitle": "Subtitle - defined by customer",
#      "body": "This notification includes image."
#    },
#    "mutable-content": 1,
#    "thread-id": "'"$SERVICE_ID"'",
#    "category": "APPIO_DEFAULT",
#    "sound": "default"
#  },
#  "data": {
#    "notification_id": "ntf_000000000000000000000'"$RANDOM_ID"'",
#    "service_id": "'"$SERVICE_ID"'",
#    "logo": "https://cdn.appio.so/app/demo.appio.so/logo.png",
#    "image_url": "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
#    "link": "https://appio.so"
#  }
#}' \
#--http2 \
#https://api.push.apple.com/3/device/$APN_TOKEN


# Background - used to trigger refresh
 curl -v \
 -H "apns-topic: $TOPIC" \
 -H "authorization: bearer $JWT_TOKEN" \
 -H "apns-push-type: background" \
 -H "apns-priority: 5" \
 -d '{
   "aps": {
     "content-available": 1
   }
 }' \
 --http2 \
 https://api.push.apple.com/3/device/$APN_TOKEN

