#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
JWT="$SCRIPT_DIR/../.keys/jwt/jwt"

AUTH_FILE="$SCRIPT_DIR/../.keys/AuthKey_Sandbox_59H9GFLJC4.p8"
AUTH_KEY_ID="59H9GFLJC4"
TEAM_ID="V9P4DCJF8Z"
TOPIC="so.appio.app"
APN_TOKEN="80b08a77af5ad1d9a6d379293b6bf7054c468e649b1825dc5171a4648eec73c2911038088c4cb493bb6d1b6d5c9ffcac429be14704ca58b73bd55463c68abfb0158ab2d42d0e941a060cfd308d18ec5b"
SERVICE_ID="demo_svc_01jwxdqaej53tkrza683h5jq35"

# ----------------------------------------

echo "🏝️ SANDBOX"


JWT_TOKEN=$($JWT -team_id=$TEAM_ID -auth_key_id=$AUTH_KEY_ID -auth_key_file=$AUTH_FILE)
echo $JWT_TOKEN


DATE=$(date "+%Y-%m-%d %H:%M:%S")
RANDOM_ID=$((RANDOM % 90000 + 10000))  # generates 10000–99999
echo "DATE: $DATE"
echo "Notification ID: ntf_000000000000000000000$RANDOM_ID"


# Foreground with image and sound
# Delivered to watch only if iphone is locked
# On watch the logo takes whole screen
curl -v \
-H "apns-topic: $TOPIC" \
-H "authorization: bearer $JWT_TOKEN" \
-H "apns-push-type: alert" \
-d '{
  "aps": {
    "alert": {
      "title": "Service Name",
      "subtitle": "Subtitle - '"$DATE"'",
      "body": "This notification includes image."
    },
    "mutable-content": 1,
    "thread-id": "'"$SERVICE_ID"'",
    "category": "APPIO_DEFAULT",
    "sound": "default"
  },
  "data": {
    "notification_id": "ntf_000000000000000000000'"$RANDOM_ID"'",
    "service_id": "'"$SERVICE_ID"'",
    "logo": "https://cdn.appio.so/app/demo.appio.so/logo.png",
    "image_url": "https://cdn.appio.so/app/demo.appio.so/banner.jpg",
    "link": "https://appio.so"
  }
}' \
--http2 \
https://api.sandbox.push.apple.com/3/device/$APN_TOKEN


## Live Activity - NOT WORKING YET. review payload according to: https://developer.apple.com/documentation/ActivityKit/starting-and-updating-live-activities-with-activitykit-push-notifications
#curl -v \
#-H "apns-topic: $TOPIC" \
#-H "authorization: bearer $JWT_TOKEN" \
#-H "apns-push-type: alert" \
#-d '{
#  "aps": {
#    "timestamp": **********,
#    "event": "update",
#    "content-state": {
#      "currentHealthLevel": 0.0,
#      "eventDescription": "Power Panda has been knocked down!"
#    },
#    "alert": {
#      "title": {
#        "loc-key": "%@ is knocked down!",
#        "loc-args": ["Power Panda"]
#      },
#      "body": {
#        "loc-key": "Use a potion to heal %@!",
#        "loc-args": ["Power Panda"]
#      },
#      "sound": "chime.aiff"
#    }
#  },
#  "data": {
#    "notification_id": "ntf_00000000000000000000000000",
#    "service_id": "svc_00000000000000000000000000"
#  }
#}' \
#--http2 \
#https://api.sandbox.push.apple.com/3/device/$APN_TOKEN


# Foreground without media. DON'T USE since we can't track delivery
# curl -v \
# -H "apns-topic: $TOPIC" \
# -H "authorization: bearer $JWT_TOKEN" \
# -H "apns-push-type: alert" \
# -d '{
#   "aps": {
#     "alert": {
#       "title": "Hello!",
#       "body": "This is a real push notification!"
#     },
#     "thread-id": "svc_00000000000000000000000000",
#     "sound": "default"
#   },
#   "data": {
#     "notification_id": "ntf_00000000000000000000000000",
#     "service_id": "svc_00000000000000000000000000"
#   }
# }' \
# --http2 \
# https://api.sandbox.push.apple.com/3/device/$APN_TOKEN


# #Background - used to trigger refresh
# curl -v \
# -H "apns-topic: $TOPIC" \
# -H "authorization: bearer $JWT_TOKEN" \
# -H "apns-push-type: background" \
# -H "apns-priority: 5" \
# -d '{
#   "aps": {
#     "content-available": 1
#   }
# }' \
# --http2 \
# https://api.sandbox.push.apple.com/3/device/$APN_TOKEN
#
