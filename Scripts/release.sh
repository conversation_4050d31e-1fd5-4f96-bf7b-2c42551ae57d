#!/bin/bash

# Colors
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

print_info() {
  echo -e ""
  echo -e "${BLUE}==================================================================${NC}"
  echo -e "${BLUE} ℹ️   $1${NC}"
  echo -e "${BLUE}==================================================================${NC}"
  echo -e ""
}

print_success() {
  echo -e ""
  echo -e "${GREEN}==================================================================${NC}"
  echo -e "${GREEN} ✅   $1${NC}"
  echo -e "${GREEN}==================================================================${NC}"
  echo -e ""
}

print_error() {
  echo -e ""
  echo -e "${RED}==================================================================${NC}"
  echo -e "${RED} ❌   $1${NC}"
  echo -e "${RED}==================================================================${NC}"
  echo -e ""
}

#################################################################

print_error "DOESN'T WORK"


#print_info "Deploy to App Store..."
#fastlane release --verbose

print_info "Deploy to TestFlight..."
fastlane beta --verbose

if [ $? -ne 0 ]; then
  print_error "Release failed"
  exit 1
fi

print_success "Release completed!"
